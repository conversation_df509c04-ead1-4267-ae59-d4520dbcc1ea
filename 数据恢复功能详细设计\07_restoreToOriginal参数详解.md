# restoreToOriginal 参数详解

## 1. 参数概述

`restoreToOriginal` 是数据恢复接口中的一个重要参数，用于控制恢复的数据应该放置在什么位置。

### 参数定义
```java
private boolean restoreToOriginal = true; // 默认值为true
```

## 2. 两种恢复模式对比

### 2.1 恢复到原始位置 (`restoreToOriginal: true`)

**适用场景：**
- 用户确定要将数据完全恢复到删除前的状态
- 误删除后的快速恢复
- 用户对原有的分类和组织结构满意

**恢复效果：**
```json
// 恢复前（已删除状态）
{
  "id": 12345,
  "name": "商品标签模板",
  "category": "personal_templates",
  "tags": "商品,价格",
  "deletedAt": "2024-07-15T10:30:00",
  "deletedBy": 1001
}

// 恢复后（restoreToOriginal: true）
{
  "id": 12345,
  "name": "商品标签模板",
  "category": "personal_templates",     // 保持原始分类
  "originalCategory": null,            // 无需记录原始分类
  "tags": "商品,价格",                  // 保持原始标签
  "isRecovered": false,                // 不标记为恢复数据
  "recoveredAt": null,                 // 无恢复时间标记
  "deletedAt": null,                   // 清除删除标记
  "deletedBy": null
}
```

### 2.2 恢复到恢复区域 (`restoreToOriginal: false`)

**适用场景：**
- 批量恢复大量数据时，避免与现有数据混淆
- 用户想要重新整理恢复的数据
- 不确定恢复的数据是否还需要，先放到临时区域
- 恢复的数据可能与现有数据有冲突

**恢复效果：**
```json
// 恢复前（已删除状态）
{
  "id": 12345,
  "name": "商品标签模板",
  "category": "personal_templates",
  "tags": "商品,价格",
  "deletedAt": "2024-07-15T10:30:00",
  "deletedBy": 1001
}

// 恢复后（restoreToOriginal: false）
{
  "id": 12345,
  "name": "商品标签模板",
  "category": "recovered_templates",   // 特殊的恢复分类
  "originalCategory": "personal_templates", // 记录原始分类
  "tags": "商品,价格,已恢复",           // 自动添加恢复标签
  "isRecovered": true,                 // 标记为恢复数据
  "recoveredAt": "2024-07-17T14:20:00", // 记录恢复时间
  "deletedAt": null,                   // 清除删除标记
  "deletedBy": null
}
```

## 3. 用户界面展示差异

### 3.1 `restoreToOriginal: true` 的界面效果

**个人模板页面：**
```
📁 个人模板
├── 📄 商品标签模板        ← 直接出现在原位置
├── 📄 价格标签模板
└── 📄 促销标签模板
```

**用户体验：**
- ✅ 数据无缝回到原位置
- ✅ 不需要额外操作
- ❌ 可能与现有数据混淆（如果用户在删除后又创建了同类数据）

### 3.2 `restoreToOriginal: false` 的界面效果

**模板页面结构：**
```
📁 个人模板
├── 📄 价格标签模板
└── 📄 促销标签模板

📁 已恢复模板 (新增分类)
└── 📄 商品标签模板 🔄     ← 带恢复标识
```

**用户体验：**
- ✅ 清楚知道哪些是恢复的数据
- ✅ 可以重新整理和分类
- ✅ 避免与现有数据混淆
- ❌ 需要用户手动移动到目标位置

## 4. 实际使用建议

### 4.1 推荐使用场景

| 场景 | 推荐值 | 理由 |
|------|--------|------|
| 单个数据恢复 | `true` | 通常是误删除，直接恢复到原位置 |
| 少量数据恢复（<10个） | `true` | 数量少，不会造成混淆 |
| 大量数据恢复（>10个） | `false` | 避免界面混乱，便于重新整理 |
| 按日期范围批量恢复 | `false` | 通常数量较多，建议先放恢复区域 |
| 不确定是否需要的数据 | `false` | 先恢复到临时区域，确认后再移动 |

### 4.2 前端交互建议

**单个恢复界面：**
```html
<div class="restore-options">
  <label>
    <input type="radio" name="restoreMode" value="true" checked>
    恢复到原始位置
  </label>
  <label>
    <input type="radio" name="restoreMode" value="false">
    恢复到"已恢复"区域
  </label>
</div>
```

**批量恢复界面：**
```html
<div class="batch-restore-tip">
  <p>💡 建议：批量恢复的数据将放入"已恢复"区域，您可以稍后重新整理</p>
  <label>
    <input type="checkbox" name="restoreToOriginal" value="false" checked>
    恢复到"已恢复"区域（推荐）
  </label>
</div>
```

## 5. 后续操作支持

### 5.1 从恢复区域移动到原位置

**API接口：**
```http
POST /api/v1/templates/move-from-recovery
{
  "templateIds": [12345, 12346],
  "targetCategory": "personal_templates"
}
```

**实现逻辑：**
```java
public void moveFromRecoveryArea(List<Long> templateIds, String targetCategory) {
    for (Long templateId : templateIds) {
        UserTemplate template = templateMapper.selectById(templateId);
        if (template.getIsRecovered()) {
            // 移动到目标分类
            template.setCategory(targetCategory);
            // 清除恢复标记
            template.setIsRecovered(false);
            template.setRecoveredAt(null);
            // 移除恢复标签
            template.setTags(template.getTags().replace(",已恢复", ""));
            
            templateMapper.updateById(template);
        }
    }
}
```

### 5.2 批量清理恢复区域

**定时任务：**
```java
@Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点
public void cleanupRecoveryArea() {
    // 清理30天前恢复但未移动的数据
    LocalDateTime cutoff = LocalDateTime.now().minusDays(30);
    
    List<UserTemplate> oldRecoveredTemplates = 
        templateMapper.selectOldRecoveredTemplates(cutoff);
    
    for (UserTemplate template : oldRecoveredTemplates) {
        // 自动移回原始分类
        template.setCategory(template.getOriginalCategory());
        template.setIsRecovered(false);
        template.setRecoveredAt(null);
        templateMapper.updateById(template);
    }
}
```

## 6. 监控和统计

### 6.1 恢复模式统计

```java
// 统计不同恢复模式的使用情况
public RecoveryModeStats getRecoveryModeStats(Long userId, LocalDate startDate, LocalDate endDate) {
    return RecoveryModeStats.builder()
        .totalRecoveries(getTotalRecoveries(userId, startDate, endDate))
        .originalPositionRecoveries(getOriginalPositionRecoveries(userId, startDate, endDate))
        .recoveryAreaRecoveries(getRecoveryAreaRecoveries(userId, startDate, endDate))
        .build();
}
```

### 6.2 用户行为分析

- 统计用户更倾向于使用哪种恢复模式
- 分析恢复区域数据的后续处理情况
- 优化默认值和推荐策略

---

通过 `restoreToOriginal` 参数，我们为用户提供了灵活的数据恢复选择，既满足了快速恢复的需求，也考虑了大量数据恢复时的组织管理需要。
