# 数据恢复功能 - 软删除机制设计

## 1. 软删除机制概述

### 1.1 设计目标
- 统一的软删除接口，支持模板、图片、文件等多种数据类型
- 保证数据安全，避免误删除造成的数据丢失
- 支持批量删除和批量恢复操作
- 提供灵活的权限控制机制

### 1.2 核心原则
- **非破坏性删除**：所有删除操作都是逻辑删除，不进行物理删除
- **可追溯性**：记录删除时间、删除用户等关键信息
- **权限分离**：VIP用户和免费用户不同的恢复权限
- **性能优化**：软删除不影响正常业务查询性能

## 2. 软删除接口设计

### 2.1 通用软删除接口

```java
/**
 * 通用软删除服务接口
 */
public interface SoftDeleteService<T> {
    
    /**
     * 软删除单个实体
     * @param id 实体ID
     * @param userId 操作用户ID
     * @return 删除结果
     */
    SoftDeleteResult softDelete(Long id, Long userId);
    
    /**
     * 批量软删除
     * @param ids 实体ID列表
     * @param userId 操作用户ID
     * @return 批量删除结果
     */
    BatchSoftDeleteResult batchSoftDelete(List<Long> ids, Long userId);
    
    /**
     * 恢复单个实体
     * @param id 实体ID
     * @param userId 操作用户ID
     * @return 恢复结果
     */
    RestoreResult restore(Long id, Long userId);
    
    /**
     * 批量恢复（按ID列表）
     * @param ids 实体ID列表
     * @param userId 操作用户ID
     * @return 批量恢复结果
     */
    BatchRestoreResult batchRestore(List<Long> ids, Long userId);

    /**
     * 按日期范围批量恢复
     * @param userId 操作用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param maxItems 最大恢复数量
     * @return 批量恢复结果
     */
    BatchRestoreResult batchRestoreByDateRange(Long userId, LocalDateTime startDate,
                                             LocalDateTime endDate, Integer maxItems);
    
    /**
     * 查询已删除的数据
     * @param userId 用户ID
     * @param queryParam 查询参数
     * @return 已删除数据列表
     */
    PageResult<T> queryDeleted(Long userId, DeletedDataQueryParam queryParam);
}
```

### 2.2 软删除结果类

```java
/**
 * 软删除操作结果
 */
@Data
public class SoftDeleteResult {
    private boolean success;
    private String message;
    private Long entityId;
    private LocalDateTime deletedAt;
    private String errorCode;
    
    public static SoftDeleteResult success(Long entityId, LocalDateTime deletedAt) {
        SoftDeleteResult result = new SoftDeleteResult();
        result.setSuccess(true);
        result.setEntityId(entityId);
        result.setDeletedAt(deletedAt);
        return result;
    }
    
    public static SoftDeleteResult failure(String errorCode, String message) {
        SoftDeleteResult result = new SoftDeleteResult();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setMessage(message);
        return result;
    }
}

/**
 * 批量软删除结果
 */
@Data
public class BatchSoftDeleteResult {
    private int totalCount;
    private int successCount;
    private int failureCount;
    private List<SoftDeleteResult> results;
    private List<String> errorMessages;
}
```

## 3. 具体实现类

### 3.1 模板软删除服务

```java
@Service
@Transactional
public class TemplateSoftDeleteService implements SoftDeleteService<UserTemplate> {
    
    @Autowired
    private UserTemplateMapper templateMapper;
    
    @Autowired
    private DataRecoveryLogService recoveryLogService;
    
    @Autowired
    private UserPermissionService permissionService;
    
    @Override
    public SoftDeleteResult softDelete(Long templateId, Long userId) {
        try {
            // 1. 验证模板是否存在且属于当前用户
            UserTemplate template = templateMapper.selectByIdAndUserId(templateId, userId);
            if (template == null) {
                return SoftDeleteResult.failure("TEMPLATE_NOT_FOUND", "模板不存在或无权限");
            }
            
            // 2. 检查是否已经被删除
            if (template.getDeletedAt() != null) {
                return SoftDeleteResult.failure("ALREADY_DELETED", "模板已被删除");
            }
            
            // 3. 执行软删除
            LocalDateTime now = LocalDateTime.now();
            template.setDeletedAt(now);
            template.setDeletedBy(userId);
            template.setUpdatedAt(now);
            
            int updated = templateMapper.updateById(template);
            if (updated > 0) {
                return SoftDeleteResult.success(templateId, now);
            } else {
                return SoftDeleteResult.failure("DELETE_FAILED", "删除操作失败");
            }
            
        } catch (Exception e) {
            log.error("软删除模板失败, templateId: {}, userId: {}", templateId, userId, e);
            return SoftDeleteResult.failure("SYSTEM_ERROR", "系统错误");
        }
    }
    
    @Override
    public RestoreResult restore(Long templateId, Long userId) {
        try {
            // 1. 检查用户是否有数据恢复权限
            if (!permissionService.hasDataRecoveryPermission(userId)) {
                return RestoreResult.failure("NO_PERMISSION", "无数据恢复权限");
            }
            
            // 2. 查询已删除的模板
            UserTemplate template = templateMapper.selectDeletedByIdAndUserId(templateId, userId);
            if (template == null) {
                return RestoreResult.failure("TEMPLATE_NOT_FOUND", "未找到已删除的模板");
            }
            
            // 3. 检查删除时间是否在允许恢复的范围内
            if (!isWithinRecoveryPeriod(template.getDeletedAt(), userId)) {
                return RestoreResult.failure("RECOVERY_EXPIRED", "超出数据恢复时间范围");
            }
            
            // 4. 执行恢复操作
            template.setDeletedAt(null);
            template.setDeletedBy(null);
            template.setUpdatedAt(LocalDateTime.now());
            
            int updated = templateMapper.updateById(template);
            if (updated > 0) {
                // 5. 记录恢复日志
                recoveryLogService.logRecovery(userId, "template", templateId, 
                    template.getTemplateName(), template.getDeletedAt());
                
                return RestoreResult.success(templateId);
            } else {
                return RestoreResult.failure("RESTORE_FAILED", "恢复操作失败");
            }
            
        } catch (Exception e) {
            log.error("恢复模板失败, templateId: {}, userId: {}", templateId, userId, e);
            return RestoreResult.failure("SYSTEM_ERROR", "系统错误");
        }
    }
    
    /**
     * 检查是否在恢复期限内
     */
    private boolean isWithinRecoveryPeriod(LocalDateTime deletedAt, Long userId) {
        if (deletedAt == null) return false;
        
        // VIP用户6个月内可恢复
        if (permissionService.isVipUser(userId)) {
            return deletedAt.isAfter(LocalDateTime.now().minusMonths(6));
        }
        
        // 免费用户无恢复权限
        return false;
    }
}
```

### 3.2 云端文件软删除服务

```java
@Service
@Transactional
public class CloudFileSoftDeleteService implements SoftDeleteService<CloudFile> {
    
    @Autowired
    private CloudFileMapper fileMapper;
    
    @Autowired
    private DataRecoveryLogService recoveryLogService;
    
    @Autowired
    private UserPermissionService permissionService;
    
    @Autowired
    private OssService ossService;
    
    @Override
    public SoftDeleteResult softDelete(Long fileId, Long userId) {
        // 实现逻辑类似模板删除，但需要考虑文件存储的特殊性
        // 文件本身不删除，只标记数据库记录为已删除
        // 实际文件清理由定时任务处理
        return doSoftDelete(fileId, userId);
    }
    
    @Override
    public RestoreResult restore(Long fileId, Long userId) {
        try {
            // 检查权限和恢复条件
            if (!permissionService.hasDataRecoveryPermission(userId)) {
                return RestoreResult.failure("NO_PERMISSION", "无数据恢复权限");
            }
            
            CloudFile file = fileMapper.selectDeletedByIdAndUserId(fileId, userId);
            if (file == null) {
                return RestoreResult.failure("FILE_NOT_FOUND", "未找到已删除的文件");
            }
            
            // 检查文件是否还存在于OSS中
            if (!ossService.exists(file.getFileUrl())) {
                return RestoreResult.failure("FILE_MISSING", "文件已被永久删除，无法恢复");
            }
            
            // 执行恢复
            file.setDeletedAt(null);
            file.setDeletedBy(null);
            file.setUpdatedAt(LocalDateTime.now());
            
            int updated = fileMapper.updateById(file);
            if (updated > 0) {
                recoveryLogService.logRecovery(userId, "file", fileId, 
                    file.getFileName(), file.getDeletedAt());
                return RestoreResult.success(fileId);
            }
            
            return RestoreResult.failure("RESTORE_FAILED", "恢复操作失败");
            
        } catch (Exception e) {
            log.error("恢复文件失败, fileId: {}, userId: {}", fileId, userId, e);
            return RestoreResult.failure("SYSTEM_ERROR", "系统错误");
        }
    }
}

## 4. 批量操作优化

### 4.1 批量删除实现

```java
@Override
public BatchSoftDeleteResult batchSoftDelete(List<Long> ids, Long userId) {
    BatchSoftDeleteResult batchResult = new BatchSoftDeleteResult();
    batchResult.setTotalCount(ids.size());
    batchResult.setResults(new ArrayList<>());
    batchResult.setErrorMessages(new ArrayList<>());

    int successCount = 0;
    int failureCount = 0;

    // 分批处理，避免单次事务过大
    List<List<Long>> batches = Lists.partition(ids, 50);

    for (List<Long> batch : batches) {
        try {
            // 批量查询验证
            List<UserTemplate> templates = templateMapper.selectByIdsAndUserId(batch, userId);
            Map<Long, UserTemplate> templateMap = templates.stream()
                .collect(Collectors.toMap(UserTemplate::getId, Function.identity()));

            LocalDateTime now = LocalDateTime.now();
            List<UserTemplate> toUpdate = new ArrayList<>();

            for (Long id : batch) {
                UserTemplate template = templateMap.get(id);
                if (template == null) {
                    SoftDeleteResult result = SoftDeleteResult.failure("NOT_FOUND", "模板不存在");
                    result.setEntityId(id);
                    batchResult.getResults().add(result);
                    failureCount++;
                    continue;
                }

                if (template.getDeletedAt() != null) {
                    SoftDeleteResult result = SoftDeleteResult.failure("ALREADY_DELETED", "已删除");
                    result.setEntityId(id);
                    batchResult.getResults().add(result);
                    failureCount++;
                    continue;
                }

                template.setDeletedAt(now);
                template.setDeletedBy(userId);
                template.setUpdatedAt(now);
                toUpdate.add(template);
            }

            // 批量更新
            if (!toUpdate.isEmpty()) {
                int updated = templateMapper.batchUpdate(toUpdate);
                if (updated == toUpdate.size()) {
                    for (UserTemplate template : toUpdate) {
                        batchResult.getResults().add(
                            SoftDeleteResult.success(template.getId(), now));
                        successCount++;
                    }
                } else {
                    // 部分更新失败，需要逐个检查
                    for (UserTemplate template : toUpdate) {
                        batchResult.getResults().add(
                            SoftDeleteResult.failure("PARTIAL_FAILURE", "批量操作部分失败"));
                        failureCount++;
                    }
                }
            }

        } catch (Exception e) {
            log.error("批量软删除失败, batch: {}, userId: {}", batch, userId, e);
            for (Long id : batch) {
                batchResult.getResults().add(
                    SoftDeleteResult.failure("SYSTEM_ERROR", "系统错误"));
                failureCount++;
            }
        }
    }

    batchResult.setSuccessCount(successCount);
    batchResult.setFailureCount(failureCount);
    return batchResult;
}
```

### 4.2 事务管理策略

```java
/**
 * 软删除事务管理器
 */
@Component
public class SoftDeleteTransactionManager {

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * 在事务中执行软删除操作
     */
    public <T> T executeInTransaction(Supplier<T> operation) {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        transactionTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        transactionTemplate.setTimeout(30); // 30秒超时

        return transactionTemplate.execute(status -> {
            try {
                return operation.get();
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new SoftDeleteException("软删除操作失败", e);
            }
        });
    }
}
```

## 5. 异常处理机制

### 5.1 自定义异常类

```java
/**
 * 软删除异常
 */
public class SoftDeleteException extends RuntimeException {
    private String errorCode;

    public SoftDeleteException(String message) {
        super(message);
    }

    public SoftDeleteException(String message, Throwable cause) {
        super(message, cause);
    }

    public SoftDeleteException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}

/**
 * 数据恢复异常
 */
public class DataRecoveryException extends RuntimeException {
    private String errorCode;

    public DataRecoveryException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}
```

### 5.2 全局异常处理

```java
@ControllerAdvice
public class SoftDeleteExceptionHandler {

    @ExceptionHandler(SoftDeleteException.class)
    public ResponseEntity<ApiResponse> handleSoftDeleteException(SoftDeleteException e) {
        log.error("软删除操作异常", e);
        return ResponseEntity.badRequest()
            .body(ApiResponse.error(e.getErrorCode(), e.getMessage()));
    }

    @ExceptionHandler(DataRecoveryException.class)
    public ResponseEntity<ApiResponse> handleDataRecoveryException(DataRecoveryException e) {
        log.error("数据恢复操作异常", e);
        return ResponseEntity.badRequest()
            .body(ApiResponse.error(e.getErrorCode(), e.getMessage()));
    }
}
```

## 6. 性能优化策略

### 6.1 查询优化
- 使用复合索引优化软删除数据查询
- 分页查询使用游标分页避免深度分页问题
- 缓存用户权限信息减少重复查询

### 6.2 批量操作优化
- 分批处理大量数据避免长事务
- 使用批量SQL减少数据库交互次数
- 异步处理非关键操作提升响应速度

## 7. 按日期范围批量恢复实现

### 7.1 按日期范围恢复服务

```java
/**
 * 按日期范围批量恢复实现
 */
@Override
public BatchRestoreResult batchRestoreByDateRange(Long userId, LocalDateTime startDate,
                                                LocalDateTime endDate, Integer maxItems) {
    BatchRestoreResult batchResult = new BatchRestoreResult();
    batchResult.setResults(new ArrayList<>());
    batchResult.setErrorMessages(new ArrayList<>());

    try {
        // 1. 验证用户权限
        if (!permissionService.hasDataRecoveryPermission(userId)) {
            throw new DataRecoveryException(ErrorCodes.NO_RECOVERY_PERMISSION,
                "按日期范围恢复需要VIP权限");
        }

        // 2. 查询指定日期范围内的已删除数据
        List<UserTemplate> deletedTemplates = templateMapper.selectDeletedByDateRange(
            userId, startDate, endDate, maxItems);

        // 3. 验证恢复期限
        List<UserTemplate> recoverableTemplates = deletedTemplates.stream()
            .filter(template -> permissionService.isWithinRecoveryPeriod(userId, template.getDeletedAt()))
            .collect(Collectors.toList());

        if (recoverableTemplates.size() != deletedTemplates.size()) {
            int expiredCount = deletedTemplates.size() - recoverableTemplates.size();
            batchResult.getErrorMessages().add(
                String.format("有%d个项目超出恢复期限，已跳过", expiredCount));
        }

        // 4. 批量恢复
        int successCount = 0;
        int failureCount = 0;

        for (UserTemplate template : recoverableTemplates) {
            try {
                RestoreResult result = restoreSingleTemplate(template, userId);
                if (result.isSuccess()) {
                    successCount++;
                } else {
                    failureCount++;
                    batchResult.getErrorMessages().add(
                        String.format("模板[%s]恢复失败: %s", template.getTemplateName(), result.getMessage()));
                }
            } catch (Exception e) {
                failureCount++;
                batchResult.getErrorMessages().add(
                    String.format("模板[%s]恢复异常: %s", template.getTemplateName(), e.getMessage()));
                log.error("按日期范围恢复模板失败, templateId: {}", template.getId(), e);
            }
        }

        batchResult.setTotalCount(recoverableTemplates.size());
        batchResult.setSuccessCount(successCount);
        batchResult.setFailureCount(failureCount);

        log.info("按日期范围批量恢复完成, userId: {}, 成功: {}, 失败: {}",
            userId, successCount, failureCount);

    } catch (Exception e) {
        log.error("按日期范围批量恢复失败, userId: {}", userId, e);
        batchResult.setTotalCount(0);
        batchResult.setSuccessCount(0);
        batchResult.setFailureCount(0);
        batchResult.getErrorMessages().add("批量恢复失败: " + e.getMessage());
    }

    return batchResult;
}

/**
 * 恢复单个模板
 */
private RestoreResult restoreSingleTemplate(UserTemplate template, Long userId, boolean restoreToOriginal) {
    try {
        // 清除删除标记
        template.setDeletedAt(null);
        template.setDeletedBy(null);
        template.setUpdatedAt(LocalDateTime.now());

        // 根据restoreToOriginal参数处理恢复位置
        if (!restoreToOriginal) {
            // 恢复到专门的恢复区域
            handleRestoreToRecoveryArea(template);
        }
        // 如果restoreToOriginal为true，保持原有的category和其他属性不变

        int updated = templateMapper.updateById(template);
        if (updated > 0) {
            // 记录恢复日志
            recoveryLogService.logRecovery(userId, "template", template.getId(),
                template.getTemplateName(), template.getDeletedAt(), restoreToOriginal);

            return RestoreResult.success(template.getId());
        } else {
            return RestoreResult.failure("RESTORE_FAILED", "数据库更新失败");
        }

    } catch (Exception e) {
        log.error("恢复单个模板失败, templateId: {}", template.getId(), e);
        return RestoreResult.failure("SYSTEM_ERROR", "系统错误: " + e.getMessage());
    }
}

/**
 * 处理恢复到恢复区域的逻辑
 */
private void handleRestoreToRecoveryArea(UserTemplate template) {
    // 1. 保存原始分类信息
    if (StringUtils.isEmpty(template.getOriginalCategory())) {
        template.setOriginalCategory(template.getCategory());
    }

    // 2. 设置为恢复分类
    template.setCategory("recovered_templates");

    // 3. 添加恢复标签
    String currentTags = template.getTags();
    if (StringUtils.isEmpty(currentTags)) {
        template.setTags("已恢复");
    } else if (!currentTags.contains("已恢复")) {
        template.setTags(currentTags + ",已恢复");
    }

    // 4. 设置恢复标记
    template.setIsRecovered(true);
    template.setRecoveredAt(LocalDateTime.now());

    log.info("模板恢复到恢复区域, templateId: {}, originalCategory: {}",
        template.getId(), template.getOriginalCategory());
}
```

### 7.2 数据库查询优化

```sql
-- 按日期范围查询已删除数据的SQL
SELECT * FROM user_templates
WHERE user_id = ?
  AND deleted_at IS NOT NULL
  AND deleted_at BETWEEN ? AND ?
  AND deleted_at > DATE_SUB(NOW(), INTERVAL 6 MONTH) -- VIP用户6个月限制
ORDER BY deleted_at DESC
LIMIT ?;

-- 对应的Mapper方法
@Select("SELECT * FROM user_templates WHERE user_id = #{userId} " +
        "AND deleted_at IS NOT NULL " +
        "AND deleted_at BETWEEN #{startDate} AND #{endDate} " +
        "AND deleted_at > DATE_SUB(NOW(), INTERVAL 6 MONTH) " +
        "ORDER BY deleted_at DESC LIMIT #{maxItems}")
List<UserTemplate> selectDeletedByDateRange(@Param("userId") Long userId,
                                          @Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate,
                                          @Param("maxItems") Integer maxItems);
```
