# XPrinter数据恢复功能 - 详细设计总览与实施指南

## 1. 项目概述

### 1.1 功能目标
- **VIP用户专享**：6个月内数据恢复功能
- **多数据类型支持**：模板、图片、文件恢复
- **灵活恢复方式**：单个恢复、批量恢复、按日期范围恢复
- **分层存储架构**：本地存储 → 热存储 → 冷存储
- **权限精确控制**：免费用户无恢复权限，VIP用户6个月期限

### 1.2 技术架构
```
移动端 (本地存储3天) 
    ↓ 同步
云端热存储 (MySQL + Redis, 6个月)
    ↓ 归档
云端冷存储 (阿里云OSS, 6个月以上)
```

## 2. 核心设计文档

### 2.1 文档结构
```
数据恢复功能详细设计/
├── 01_数据库设计.md           # 数据库表结构、索引、约束设计
├── 02_软删除机制设计.md       # 统一软删除接口、批量操作实现
├── 03_API接口设计.md          # REST API接口、请求响应格式
├── 04_权限控制与VIP验证.md    # 用户权限体系、VIP验证机制
├── 05_分层存储架构实现方案.md # 本地、热存储、冷存储实现
├── 06_数据清理与归档策略.md   # 自动清理任务、归档策略
└── 00_设计总览与实施指南.md   # 本文档
```

### 2.2 关键技术决策

#### 软删除机制
- 所有可恢复数据采用软删除（`deleted_at`字段）
- 保留原始创建时间确保恢复后排序正确
- 统一的软删除服务接口支持多种数据类型

#### 权限控制
- 基于用户类型和权限表的双重验证
- 权限信息缓存2小时减少数据库查询
- 切面编程实现权限验证的统一拦截

#### 分层存储
- **本地存储**：SQLite，3天内数据，离线可用
- **热存储**：MySQL + Redis，6个月内数据，高性能查询
- **冷存储**：阿里云OSS冷归档，6个月以上数据，成本优化

## 3. 核心API接口

### 3.1 主要接口列表

| 接口路径 | 方法 | 功能描述 | 权限要求 |
|---------|------|----------|----------|
| `/api/v1/data-recovery/deleted` | GET | 查询已删除数据 | 登录用户 |
| `/api/v1/data-recovery/restore` | POST | 恢复单个数据 | VIP用户 |
| `/api/v1/data-recovery/batch-restore` | POST | 批量恢复（按ID） | VIP用户 |
| `/api/v1/data-recovery/batch-restore-by-date` | POST | 按日期范围批量恢复 | VIP用户 |
| `/api/v1/data-recovery/permission` | GET | 检查恢复权限 | 登录用户 |
| `/api/v1/data-recovery/statistics` | GET | 恢复统计信息 | 登录用户 |

### 3.2 按日期范围批量恢复（新增功能）

```json
POST /api/v1/data-recovery/batch-restore-by-date
{
  "dataType": "template",
  "dateRange": {
    "start": "2024-01-01",
    "end": "2024-07-18"
  },
  "restoreToOriginal": true,
  "maxItems": 100
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalCount": 25,
    "successCount": 23,
    "failureCount": 2,
    "results": [...],
    "errorMessages": ["模板[商品标签]恢复失败: 文件已损坏"]
  }
}
```

## 4. 数据库核心表

### 4.1 主要数据表

| 表名 | 用途 | 关键字段 |
|------|------|----------|
| `user_templates` | 用户模板 | `deleted_at`, `deleted_by` |
| `cloud_files` | 云端文件 | `deleted_at`, `deleted_by` |
| `user_permissions` | 用户权限 | `permission_type`, `expires_at` |
| `data_recovery_logs` | 恢复记录 | `recovery_type`, `target_id` |
| `archive_records` | 归档记录 | `archive_key`, `original_id` |

### 4.2 关键索引优化

```sql
-- 用户已删除数据查询优化
CREATE INDEX idx_user_deleted_created ON user_templates(user_id, deleted_at, created_at);

-- 数据恢复时间范围查询优化  
CREATE INDEX idx_deleted_range ON user_templates(deleted_at, user_id);
```

## 5. 实施计划

### 5.1 开发阶段（建议3周完成）

**第1周：基础架构**
- [ ] 数据库表结构创建和迁移脚本
- [ ] 软删除机制基础服务实现
- [ ] 用户权限服务和VIP验证逻辑
- [ ] 基础API接口（查询、单个恢复）

**第2周：核心功能**
- [ ] 批量恢复功能实现
- [ ] 按日期范围批量恢复功能
- [ ] 分层存储服务实现
- [ ] 权限验证切面和异常处理

**第3周：优化完善**
- [ ] 数据清理和归档任务
- [ ] 性能优化和缓存策略
- [ ] 单元测试和集成测试
- [ ] API文档和部署脚本

### 5.2 测试重点

**功能测试**
- VIP用户可以恢复6个月内删除的数据
- 免费用户无法访问数据恢复功能
- 按日期范围批量恢复的准确性
- 数据归档和清理的正确性

**性能测试**
- 大量数据的批量恢复性能
- 分页查询的响应时间
- 并发恢复操作的稳定性

**安全测试**
- 权限验证的有效性
- 越权访问的防护
- 数据恢复的完整性

## 6. 运维监控

### 6.1 关键监控指标
- 数据恢复成功率
- 批量恢复平均耗时
- 存储空间使用情况
- 权限验证失败次数

### 6.2 告警策略
- 恢复成功率低于95%时告警
- 单次批量恢复超过30秒告警
- 冷存储归档失败时告警
- 权限验证异常频繁时告警

## 7. 风险评估与应对

### 7.1 技术风险
| 风险项 | 风险等级 | 应对策略 |
|--------|----------|----------|
| 大量数据恢复性能问题 | 中 | 分批处理、异步执行 |
| 冷存储数据损坏 | 中 | 多副本存储、校验机制 |
| 权限验证绕过 | 高 | 多层验证、审计日志 |
| 数据库锁竞争 | 中 | 读写分离、索引优化 |

### 7.2 业务风险
- **用户期望管理**：明确告知6个月恢复期限
- **成本控制**：合理设置批量恢复数量限制
- **数据合规**：确保数据删除符合隐私法规

## 8. 后续优化方向

### 8.1 功能增强
- 支持恢复到指定位置
- 增加恢复预览功能
- 提供恢复进度通知
- 支持恢复历史版本

### 8.2 性能优化
- 引入消息队列处理大批量恢复
- 实现智能预加载机制
- 优化冷存储数据检索速度
- 增加CDN加速文件恢复

---

**文档版本**: V1.0  
**最后更新**: 2024-07-17  
**负责人**: 后端开发团队  
**审核状态**: 待审核
