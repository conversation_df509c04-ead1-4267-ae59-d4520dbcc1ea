# 数据恢复功能 - 数据清理与归档策略

## 1. 数据清理策略概述

### 1.1 清理策略分层

```java
/**
 * 数据清理策略枚举
 */
public enum DataCleanupStrategy {
    IMMEDIATE("立即清理", 0),
    SOFT_DELETE("软删除", 1),
    ARCHIVE_THEN_DELETE("归档后删除", 2),
    PERMANENT_ARCHIVE("永久归档", 3);
    
    private final String description;
    private final int level;
    
    DataCleanupStrategy(String description, int level) {
        this.description = description;
        this.level = level;
    }
}

/**
 * 数据清理规则配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "data.cleanup")
public class DataCleanupConfig {
    
    // 免费用户数据清理规则
    private FreeUserCleanupRule freeUser = new FreeUserCleanupRule();
    
    // VIP用户数据清理规则
    private VipUserCleanupRule vipUser = new VipUserCleanupRule();
    
    // 系统级清理规则
    private SystemCleanupRule system = new SystemCleanupRule();
    
    @Data
    public static class FreeUserCleanupRule {
        private int templateLimit = 100; // 模板数量限制
        private int printLogDays = 30; // 打印日志保留天数
        private boolean enableDataRecovery = false; // 是否启用数据恢复功能
        private int dataRetentionMonths = 6; // 数据保留期限（与VIP一致，为转换做准备）
    }
    
    @Data
    public static class VipUserCleanupRule {
        private int templateLimit = -1; // 无限制
        private int printLogDays = 100; // 打印日志保留天数
        private int dataRecoveryMonths = 6; // 数据恢复期限（月）
        private int archiveAfterMonths = 6; // 归档期限（月）
    }
    
    @Data
    public static class SystemCleanupRule {
        private int batchSize = 1000; // 批处理大小
        private int maxRetryTimes = 3; // 最大重试次数
        private String cleanupCron = "0 0 2 * * ?"; // 清理任务执行时间
        private boolean enableParallelProcessing = true; // 是否启用并行处理
    }
}
```

### 1.2 清理时间线设计

```
时间线：
0天 ────── 3天 ────── 30天 ────── 6个月 ────── 1年 ────── 永久
 │         │         │          │          │         │
活跃期    本地清理   热存储期    冷归档期    清理期    永久归档

免费用户：
- 0-6个月：数据保留期（无恢复权限，但为转VIP做准备）
- 6个月-1年：冷归档期（转VIP后可恢复）
- 1年后：数据清理

VIP用户：
- 0-6个月：数据恢复期（热存储）
- 6个月-1年：冷归档期（冷存储）
- 1年后：可选择永久归档或清理

转换策略：
- 免费用户转VIP后，立即获得6个月内数据的恢复权限
- 已归档到冷存储的数据也可以恢复
```

## 2. 自动清理任务实现

### 2.1 清理任务调度器

```java
/**
 * 数据清理任务调度器
 */
@Component
@EnableScheduling
@Slf4j
public class DataCleanupScheduler {
    
    @Autowired
    private DataCleanupService cleanupService;
    
    @Autowired
    private DataCleanupConfig cleanupConfig;
    
    @Autowired
    private CleanupTaskManager taskManager;
    
    /**
     * 每日数据清理任务
     */
    @Scheduled(cron = "${data.cleanup.system.cleanup-cron:0 0 2 * * ?}")
    public void dailyCleanupTask() {
        log.info("开始执行每日数据清理任务");
        
        try {
            // 1. 归档所有用户过期数据（免费用户和VIP用户策略一致）
            archiveExpiredData();

            // 2. 清理超过1年的归档数据
            cleanupOldArchivedData();

            // 3. 清理系统临时数据
            cleanupSystemTempData();

            // 4. 清理失败任务重试
            retryFailedCleanupTasks();
            
            log.info("每日数据清理任务执行完成");
            
        } catch (Exception e) {
            log.error("每日数据清理任务执行失败", e);
        }
    }
    
    /**
     * 归档所有用户过期数据（免费用户和VIP用户策略一致）
     */
    private void archiveExpiredData() {
        log.info("开始归档所有用户过期数据");

        // 创建归档任务
        CleanupTask task = taskManager.createTask(
            CleanupTaskType.DATA_ARCHIVE,
            "归档过期数据",
            LocalDate.now().minusMonths(6) // 6个月前的数据
        );

        try {
            taskManager.startTask(task.getId());

            // 执行归档（不区分用户类型）
            ArchiveResult result = cleanupService.archiveExpiredData(6);

            taskManager.completeTask(task.getId(), result);
            log.info("过期数据归档完成, 归档数量: {}", result.getArchivedCount());

        } catch (Exception e) {
            taskManager.failTask(task.getId(), e.getMessage());
            log.error("过期数据归档失败", e);
        }
    }
    
    /**
     * 清理超过1年的归档数据
     */
    private void cleanupOldArchivedData() {
        log.info("开始清理超过1年的归档数据");

        CleanupTask task = taskManager.createTask(
            CleanupTaskType.OLD_DATA_CLEANUP,
            "清理超过1年的归档数据",
            LocalDate.now().minusYears(1)
        );

        try {
            taskManager.startTask(task.getId());

            // 执行清理
            CleanupResult result = cleanupService.cleanupOldArchivedData(12);

            taskManager.completeTask(task.getId(), result);
            log.info("超过1年的归档数据清理完成, 清理数量: {}", result.getCleanedCount());

        } catch (Exception e) {
            taskManager.failTask(task.getId(), e.getMessage());
            log.error("超过1年的归档数据清理失败", e);
        }
    }
    
    /**
     * 清理系统临时数据
     */
    private void cleanupSystemTempData() {
        log.info("开始清理系统临时数据");
        
        try {
            // 清理临时文件
            cleanupService.cleanupTempFiles();
            
            // 清理过期缓存
            cleanupService.cleanupExpiredCache();
            
            // 清理日志文件
            cleanupService.cleanupLogFiles();
            
            log.info("系统临时数据清理完成");
            
        } catch (Exception e) {
            log.error("系统临时数据清理失败", e);
        }
    }
    
    /**
     * 重试失败的清理任务
     */
    private void retryFailedCleanupTasks() {
        log.info("开始重试失败的清理任务");
        
        try {
            List<CleanupTask> failedTasks = taskManager.getFailedTasks(
                LocalDateTime.now().minusDays(1)); // 重试1天内的失败任务
            
            for (CleanupTask task : failedTasks) {
                if (task.getRetryCount() < cleanupConfig.getSystem().getMaxRetryTimes()) {
                    taskManager.retryTask(task.getId());
                    log.info("重试清理任务: {}", task.getId());
                }
            }
            
        } catch (Exception e) {
            log.error("重试失败清理任务异常", e);
        }
    }
}
```

### 2.2 数据清理服务实现

```java
/**
 * 数据清理服务实现
 */
@Service
@Transactional
public class DataCleanupServiceImpl implements DataCleanupService {
    
    @Autowired
    private UserTemplateMapper templateMapper;
    
    @Autowired
    private CloudFileMapper fileMapper;
    
    @Autowired
    private PrintLogMapper printLogMapper;
    
    @Autowired
    private ColdStorageService coldStorageService;
    
    @Autowired
    private DataCleanupConfig cleanupConfig;
    
    @Override
    public ArchiveResult archiveExpiredData(int archiveAfterMonths) {
        ArchiveResult result = new ArchiveResult();
        LocalDateTime cutoffTime = LocalDateTime.now().minusMonths(archiveAfterMonths);

        try {
            // 1. 归档所有用户过期的已删除模板（不区分用户类型）
            List<UserTemplate> expiredTemplates = templateMapper.selectAllExpiredDeleted(cutoffTime);
            int archivedTemplates = 0;

            for (UserTemplate template : expiredTemplates) {
                try {
                    coldStorageService.archiveTemplate(template);
                    templateMapper.markAsArchived(template.getId());
                    archivedTemplates++;
                } catch (Exception e) {
                    log.error("模板归档失败, templateId: {}", template.getId(), e);
                    result.addFailedItem("template", template.getId(), e.getMessage());
                }
            }

            // 2. 归档所有用户过期的已删除文件
            List<CloudFile> expiredFiles = fileMapper.selectAllExpiredDeleted(cutoffTime);
            int archivedFiles = 0;

            for (CloudFile file : expiredFiles) {
                try {
                    coldStorageService.archiveFile(file);
                    fileMapper.markAsArchived(file.getId());
                    archivedFiles++;
                } catch (Exception e) {
                    log.error("文件归档失败, fileId: {}", file.getId(), e);
                    result.addFailedItem("file", file.getId(), e.getMessage());
                }
            }

            result.addArchivedCount("templates", archivedTemplates);
            result.addArchivedCount("files", archivedFiles);
            result.setSuccess(true);
            result.setMessage("过期数据归档完成");

            log.info("过期数据归档完成, templates: {}, files: {}",
                archivedTemplates, archivedFiles);

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("归档失败: " + e.getMessage());
            log.error("过期数据归档失败", e);
        }

        return result;
    }
    
    @Override
    public CleanupResult cleanupOldArchivedData(int retentionMonths) {
        CleanupResult result = new CleanupResult();
        LocalDateTime cutoffTime = LocalDateTime.now().minusMonths(retentionMonths);

        try {
            // 1. 清理超过1年的已归档模板数据
            int deletedTemplates = templateMapper.permanentDeleteArchivedTemplates(cutoffTime);
            result.addCleanedCount("archived_templates", deletedTemplates);

            // 2. 清理超过1年的已归档文件数据
            int deletedFiles = fileMapper.permanentDeleteArchivedFiles(cutoffTime);
            result.addCleanedCount("archived_files", deletedFiles);

            // 3. 清理对应的OSS冷存储文件
            int deletedOssFiles = cleanupOssArchivedFiles(cutoffTime);
            result.addCleanedCount("oss_archived_files", deletedOssFiles);

            // 4. 清理归档记录
            int deletedArchiveRecords = archiveRecordMapper.deleteOldRecords(cutoffTime);
            result.addCleanedCount("archive_records", deletedArchiveRecords);

            result.setSuccess(true);
            result.setMessage("超过1年的归档数据清理完成");

            log.info("超过1年的归档数据清理完成, templates: {}, files: {}, oss_files: {}, records: {}",
                deletedTemplates, deletedFiles, deletedOssFiles, deletedArchiveRecords);

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("清理失败: " + e.getMessage());
            log.error("超过1年的归档数据清理失败", e);
        }

        return result;
    }

    /**
     * 清理OSS中的归档文件
     */
    private int cleanupOssArchivedFiles(LocalDateTime cutoffTime) {
        try {
            List<ArchiveRecord> oldRecords = archiveRecordMapper.selectOldRecords(cutoffTime);
            int deletedCount = 0;

            for (ArchiveRecord record : oldRecords) {
                try {
                    ossClient.deleteObject(ARCHIVE_BUCKET, record.getArchiveKey());
                    deletedCount++;
                    log.debug("删除OSS归档文件: {}", record.getArchiveKey());
                } catch (Exception e) {
                    log.error("删除OSS归档文件失败, key: {}", record.getArchiveKey(), e);
                }
            }

            return deletedCount;
        } catch (Exception e) {
            log.error("清理OSS归档文件失败", e);
            return 0;
        }
    }
    
    @Override
    public void cleanupTempFiles() {
        try {
            // 清理上传临时文件
            String tempDir = System.getProperty("java.io.tmpdir") + "/xprinter/uploads";
            File tempDirFile = new File(tempDir);
            
            if (tempDirFile.exists() && tempDirFile.isDirectory()) {
                File[] tempFiles = tempDirFile.listFiles();
                if (tempFiles != null) {
                    long cutoffTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000L; // 24小时前
                    
                    for (File file : tempFiles) {
                        if (file.lastModified() < cutoffTime) {
                            if (file.delete()) {
                                log.debug("删除临时文件: {}", file.getAbsolutePath());
                            }
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("清理临时文件失败", e);
        }
    }
    
    @Override
    public void cleanupExpiredCache() {
        try {
            // 这里可以添加清理Redis过期缓存的逻辑
            // 通常Redis会自动清理过期key，这里主要是清理一些特殊的缓存
            
            log.info("过期缓存清理完成");
            
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
        }
    }
    
    @Override
    public void cleanupLogFiles() {
        try {
            // 清理应用日志文件（保留最近30天）
            String logDir = "logs";
            File logDirFile = new File(logDir);
            
            if (logDirFile.exists() && logDirFile.isDirectory()) {
                File[] logFiles = logDirFile.listFiles((dir, name) -> name.endsWith(".log"));
                if (logFiles != null) {
                    long cutoffTime = System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L; // 30天前
                    
                    for (File file : logFiles) {
                        if (file.lastModified() < cutoffTime) {
                            if (file.delete()) {
                                log.debug("删除过期日志文件: {}", file.getName());
                            }
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("清理日志文件失败", e);
        }
    }
}
```
