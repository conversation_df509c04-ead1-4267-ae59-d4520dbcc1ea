# 数据恢复功能 - 数据清理与归档策略

## 1. 数据清理策略概述

### 1.1 清理策略分层

```java
/**
 * 数据清理策略枚举
 */
public enum DataCleanupStrategy {
    IMMEDIATE("立即清理", 0),
    SOFT_DELETE("软删除", 1),
    ARCHIVE_THEN_DELETE("归档后删除", 2),
    PERMANENT_ARCHIVE("永久归档", 3);
    
    private final String description;
    private final int level;
    
    DataCleanupStrategy(String description, int level) {
        this.description = description;
        this.level = level;
    }
}

/**
 * 数据清理规则配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "data.cleanup")
public class DataCleanupConfig {
    
    // 免费用户数据清理规则
    private FreeUserCleanupRule freeUser = new FreeUserCleanupRule();
    
    // VIP用户数据清理规则
    private VipUserCleanupRule vipUser = new VipUserCleanupRule();
    
    // 系统级清理规则
    private SystemCleanupRule system = new SystemCleanupRule();
    
    @Data
    public static class FreeUserCleanupRule {
        private int templateLimit = 100; // 模板数量限制
        private int printLogDays = 30; // 打印日志保留天数
        private boolean enableDataRecovery = false; // 是否启用数据恢复
    }
    
    @Data
    public static class VipUserCleanupRule {
        private int templateLimit = -1; // 无限制
        private int printLogDays = 100; // 打印日志保留天数
        private int dataRecoveryMonths = 6; // 数据恢复期限（月）
        private int archiveAfterMonths = 6; // 归档期限（月）
    }
    
    @Data
    public static class SystemCleanupRule {
        private int batchSize = 1000; // 批处理大小
        private int maxRetryTimes = 3; // 最大重试次数
        private String cleanupCron = "0 0 2 * * ?"; // 清理任务执行时间
        private boolean enableParallelProcessing = true; // 是否启用并行处理
    }
}
```

### 1.2 清理时间线设计

```
时间线：
0天 ────── 3天 ────── 30天 ────── 6个月 ────── 1年 ────── 永久
 │         │         │          │          │         │
活跃期    本地清理   热存储期    冷归档期    清理期    永久归档

免费用户：
- 0-30天：正常使用期
- 30天后：无数据恢复，直接清理

VIP用户：
- 0-6个月：数据恢复期（热存储）
- 6个月-1年：冷归档期（冷存储）
- 1年后：可选择永久归档或清理
```

## 2. 自动清理任务实现

### 2.1 清理任务调度器

```java
/**
 * 数据清理任务调度器
 */
@Component
@EnableScheduling
@Slf4j
public class DataCleanupScheduler {
    
    @Autowired
    private DataCleanupService cleanupService;
    
    @Autowired
    private DataCleanupConfig cleanupConfig;
    
    @Autowired
    private CleanupTaskManager taskManager;
    
    /**
     * 每日数据清理任务
     */
    @Scheduled(cron = "${data.cleanup.system.cleanup-cron:0 0 2 * * ?}")
    public void dailyCleanupTask() {
        log.info("开始执行每日数据清理任务");
        
        try {
            // 1. 清理免费用户过期数据
            cleanupFreeUserExpiredData();
            
            // 2. 归档VIP用户过期数据
            archiveVipUserExpiredData();
            
            // 3. 清理系统临时数据
            cleanupSystemTempData();
            
            // 4. 清理失败任务重试
            retryFailedCleanupTasks();
            
            log.info("每日数据清理任务执行完成");
            
        } catch (Exception e) {
            log.error("每日数据清理任务执行失败", e);
        }
    }
    
    /**
     * 清理免费用户过期数据
     */
    private void cleanupFreeUserExpiredData() {
        log.info("开始清理免费用户过期数据");
        
        // 创建清理任务
        CleanupTask task = taskManager.createTask(
            CleanupTaskType.FREE_USER_CLEANUP,
            "清理免费用户过期数据",
            LocalDate.now().minusDays(cleanupConfig.getFreeUser().getPrintLogDays())
        );
        
        try {
            taskManager.startTask(task.getId());
            
            // 执行清理
            CleanupResult result = cleanupService.cleanupFreeUserExpiredData(
                cleanupConfig.getFreeUser().getPrintLogDays());
            
            taskManager.completeTask(task.getId(), result);
            log.info("免费用户过期数据清理完成, 清理数量: {}", result.getCleanedCount());
            
        } catch (Exception e) {
            taskManager.failTask(task.getId(), e.getMessage());
            log.error("免费用户过期数据清理失败", e);
        }
    }
    
    /**
     * 归档VIP用户过期数据
     */
    private void archiveVipUserExpiredData() {
        log.info("开始归档VIP用户过期数据");
        
        CleanupTask task = taskManager.createTask(
            CleanupTaskType.VIP_USER_ARCHIVE,
            "归档VIP用户过期数据",
            LocalDate.now().minusMonths(cleanupConfig.getVipUser().getArchiveAfterMonths())
        );
        
        try {
            taskManager.startTask(task.getId());
            
            // 执行归档
            ArchiveResult result = cleanupService.archiveVipUserExpiredData(
                cleanupConfig.getVipUser().getArchiveAfterMonths());
            
            taskManager.completeTask(task.getId(), result);
            log.info("VIP用户过期数据归档完成, 归档数量: {}", result.getArchivedCount());
            
        } catch (Exception e) {
            taskManager.failTask(task.getId(), e.getMessage());
            log.error("VIP用户过期数据归档失败", e);
        }
    }
    
    /**
     * 清理系统临时数据
     */
    private void cleanupSystemTempData() {
        log.info("开始清理系统临时数据");
        
        try {
            // 清理临时文件
            cleanupService.cleanupTempFiles();
            
            // 清理过期缓存
            cleanupService.cleanupExpiredCache();
            
            // 清理日志文件
            cleanupService.cleanupLogFiles();
            
            log.info("系统临时数据清理完成");
            
        } catch (Exception e) {
            log.error("系统临时数据清理失败", e);
        }
    }
    
    /**
     * 重试失败的清理任务
     */
    private void retryFailedCleanupTasks() {
        log.info("开始重试失败的清理任务");
        
        try {
            List<CleanupTask> failedTasks = taskManager.getFailedTasks(
                LocalDateTime.now().minusDays(1)); // 重试1天内的失败任务
            
            for (CleanupTask task : failedTasks) {
                if (task.getRetryCount() < cleanupConfig.getSystem().getMaxRetryTimes()) {
                    taskManager.retryTask(task.getId());
                    log.info("重试清理任务: {}", task.getId());
                }
            }
            
        } catch (Exception e) {
            log.error("重试失败清理任务异常", e);
        }
    }
}
```

### 2.2 数据清理服务实现

```java
/**
 * 数据清理服务实现
 */
@Service
@Transactional
public class DataCleanupServiceImpl implements DataCleanupService {
    
    @Autowired
    private UserTemplateMapper templateMapper;
    
    @Autowired
    private CloudFileMapper fileMapper;
    
    @Autowired
    private PrintLogMapper printLogMapper;
    
    @Autowired
    private ColdStorageService coldStorageService;
    
    @Autowired
    private DataCleanupConfig cleanupConfig;
    
    @Override
    public CleanupResult cleanupFreeUserExpiredData(int retentionDays) {
        CleanupResult result = new CleanupResult();
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);
        
        try {
            // 1. 清理免费用户的已删除模板（无恢复期）
            int deletedTemplates = templateMapper.permanentDeleteFreeUserTemplates(cutoffTime);
            result.addCleanedCount("templates", deletedTemplates);
            
            // 2. 清理免费用户的已删除文件
            int deletedFiles = fileMapper.permanentDeleteFreeUserFiles(cutoffTime);
            result.addCleanedCount("files", deletedFiles);
            
            // 3. 清理过期打印日志
            int deletedLogs = printLogMapper.deleteExpiredLogs(cutoffTime);
            result.addCleanedCount("print_logs", deletedLogs);
            
            result.setSuccess(true);
            result.setMessage("免费用户过期数据清理完成");
            
            log.info("免费用户过期数据清理完成, templates: {}, files: {}, logs: {}", 
                deletedTemplates, deletedFiles, deletedLogs);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("清理失败: " + e.getMessage());
            log.error("免费用户过期数据清理失败", e);
        }
        
        return result;
    }
    
    @Override
    public ArchiveResult archiveVipUserExpiredData(int archiveAfterMonths) {
        ArchiveResult result = new ArchiveResult();
        LocalDateTime cutoffTime = LocalDateTime.now().minusMonths(archiveAfterMonths);
        
        try {
            // 1. 归档VIP用户过期的已删除模板
            List<UserTemplate> expiredTemplates = templateMapper.selectVipExpiredDeleted(cutoffTime);
            int archivedTemplates = 0;
            
            for (UserTemplate template : expiredTemplates) {
                try {
                    coldStorageService.archiveTemplate(template);
                    templateMapper.markAsArchived(template.getId());
                    archivedTemplates++;
                } catch (Exception e) {
                    log.error("模板归档失败, templateId: {}", template.getId(), e);
                    result.addFailedItem("template", template.getId(), e.getMessage());
                }
            }
            
            // 2. 归档VIP用户过期的已删除文件
            List<CloudFile> expiredFiles = fileMapper.selectVipExpiredDeleted(cutoffTime);
            int archivedFiles = 0;
            
            for (CloudFile file : expiredFiles) {
                try {
                    coldStorageService.archiveFile(file);
                    fileMapper.markAsArchived(file.getId());
                    archivedFiles++;
                } catch (Exception e) {
                    log.error("文件归档失败, fileId: {}", file.getId(), e);
                    result.addFailedItem("file", file.getId(), e.getMessage());
                }
            }
            
            result.addArchivedCount("templates", archivedTemplates);
            result.addArchivedCount("files", archivedFiles);
            result.setSuccess(true);
            result.setMessage("VIP用户过期数据归档完成");
            
            log.info("VIP用户过期数据归档完成, templates: {}, files: {}", 
                archivedTemplates, archivedFiles);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("归档失败: " + e.getMessage());
            log.error("VIP用户过期数据归档失败", e);
        }
        
        return result;
    }
    
    @Override
    public void cleanupTempFiles() {
        try {
            // 清理上传临时文件
            String tempDir = System.getProperty("java.io.tmpdir") + "/xprinter/uploads";
            File tempDirFile = new File(tempDir);
            
            if (tempDirFile.exists() && tempDirFile.isDirectory()) {
                File[] tempFiles = tempDirFile.listFiles();
                if (tempFiles != null) {
                    long cutoffTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000L; // 24小时前
                    
                    for (File file : tempFiles) {
                        if (file.lastModified() < cutoffTime) {
                            if (file.delete()) {
                                log.debug("删除临时文件: {}", file.getAbsolutePath());
                            }
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("清理临时文件失败", e);
        }
    }
    
    @Override
    public void cleanupExpiredCache() {
        try {
            // 这里可以添加清理Redis过期缓存的逻辑
            // 通常Redis会自动清理过期key，这里主要是清理一些特殊的缓存
            
            log.info("过期缓存清理完成");
            
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
        }
    }
    
    @Override
    public void cleanupLogFiles() {
        try {
            // 清理应用日志文件（保留最近30天）
            String logDir = "logs";
            File logDirFile = new File(logDir);
            
            if (logDirFile.exists() && logDirFile.isDirectory()) {
                File[] logFiles = logDirFile.listFiles((dir, name) -> name.endsWith(".log"));
                if (logFiles != null) {
                    long cutoffTime = System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L; // 30天前
                    
                    for (File file : logFiles) {
                        if (file.lastModified() < cutoffTime) {
                            if (file.delete()) {
                                log.debug("删除过期日志文件: {}", file.getName());
                            }
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("清理日志文件失败", e);
        }
    }
}
```
