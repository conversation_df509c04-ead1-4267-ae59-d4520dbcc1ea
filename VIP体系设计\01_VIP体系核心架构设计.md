# XPrinter VIP体系核心架构设计

## 1. VIP体系概述

### 1.1 设计目标
- **分层服务**：为不同付费能力的用户提供差异化服务
- **功能解锁**：通过VIP权限解锁高级功能，提升用户体验
- **收入增长**：建立可持续的订阅收入模式
- **用户留存**：通过高价值功能增强用户粘性

### 1.2 核心原则
- **权限分离**：功能实现与权限控制分离
- **灵活配置**：支持动态调整VIP权限和限制
- **平滑升级**：免费用户可无缝升级到VIP
- **降级保护**：VIP到期后优雅降级，保护用户数据

## 2. 用户分层架构

### 2.1 用户等级定义

```java
/**
 * 用户等级枚举
 */
public enum UserTier {
    FREE("免费用户", 0, "basic"),
    VIP_MONTHLY("VIP月度", 1, "premium"),
    VIP_YEARLY("VIP年度", 2, "premium_plus"),
    VIP_LIFETIME("VIP终身", 3, "ultimate");
    
    private final String displayName;
    private final int level;
    private final String planCode;
    
    UserTier(String displayName, int level, String planCode) {
        this.displayName = displayName;
        this.level = level;
        this.planCode = planCode;
    }
    
    public boolean isVip() {
        return this.level > 0;
    }
    
    public boolean hasHigherLevelThan(UserTier other) {
        return this.level > other.level;
    }
}
```

### 2.2 VIP权益对比

| 功能类别 | 免费用户 | VIP月度 | VIP年度 | VIP终身 |
|---------|---------|---------|---------|---------|
| **基础功能** |
| 标准打印 | ✅ | ✅ | ✅ | ✅ |
| 基础模板 | ✅ | ✅ | ✅ | ✅ |
| 扫码新建 | ✅ | ✅ | ✅ | ✅ |
| 文本引用 | ✅ | ✅ | ✅ | ✅ |
| 批量打印 | ✅ | ✅ | ✅ | ✅ |
| 打印日志 | ✅ | ✅ | ✅ | ✅ |
| **存储限制** |
| 云端模板数量 | 100个 | 无限制 | 无限制 | 无限制 |
| 云端文件存储 | 1GB | 10GB | 50GB | 100GB |
| **高级功能** |
| 数据恢复 | ❌ | 6个月 | 6个月 | 6个月 |
| 识图新建 | ❌ | ✅ | ✅ | ✅ |
| 价签打印 | ❌ | ✅ | ✅ | ✅ |
| 团队协作 | ❌ | 5人 | 20人 | 50人 |
| **增值服务** |
| 广告展示 | 有 | 无 | 无 | 无 |
| 客服优先级 | 普通 | 优先 | 高优先 | 最高优先 |
| 新功能体验 | ❌ | ❌ | Beta | Alpha |

## 3. 权限体系架构

### 3.1 权限分类

```java
/**
 * 权限类型枚举
 */
public enum PermissionType {
    // 功能权限
    FEATURE_DATA_RECOVERY("数据恢复", "feature"),
    FEATURE_OCR_CREATE("识图新建", "feature"),
    FEATURE_PRICE_TAG("价签打印", "feature"),
    FEATURE_TEAM_COLLABORATION("团队协作", "feature"),
    
    // 配额权限
    QUOTA_TEMPLATE_COUNT("模板数量", "quota"),
    QUOTA_STORAGE_SIZE("存储空间", "quota"),
    QUOTA_TEAM_MEMBERS("团队成员", "quota"),
    QUOTA_OCR_MONTHLY("月度OCR次数", "quota"),
    
    // 体验权限
    EXPERIENCE_AD_FREE("无广告", "experience"),
    EXPERIENCE_PRIORITY_SUPPORT("优先客服", "experience"),
    EXPERIENCE_BETA_FEATURES("Beta功能", "experience");
    
    private final String displayName;
    private final String category;
    
    PermissionType(String displayName, String category) {
        this.displayName = displayName;
        this.category = category;
    }
}
```

### 3.2 权限配置模型

```java
/**
 * VIP权限配置
 */
@Data
@Builder
public class VipPermissionConfig {
    private UserTier userTier;
    private Map<PermissionType, PermissionValue> permissions;
    private LocalDateTime effectiveDate;
    private LocalDateTime expiryDate;
    
    @Data
    @Builder
    public static class PermissionValue {
        private boolean enabled;
        private String value; // 对于配额类权限，存储具体数值
        private Map<String, Object> metadata; // 扩展属性
        
        public static PermissionValue enabled() {
            return PermissionValue.builder().enabled(true).build();
        }
        
        public static PermissionValue disabled() {
            return PermissionValue.builder().enabled(false).build();
        }
        
        public static PermissionValue quota(String value) {
            return PermissionValue.builder().enabled(true).value(value).build();
        }
    }
}
```

## 4. 订阅模式设计

### 4.1 订阅套餐定义

```java
/**
 * VIP订阅套餐
 */
@Data
@Entity
@Table(name = "vip_plans")
public class VipPlan {
    @Id
    private String planId;
    
    @Column(name = "plan_name")
    private String planName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "user_tier")
    private UserTier userTier;
    
    @Column(name = "duration_type")
    private String durationType; // MONTHLY, YEARLY, LIFETIME
    
    @Column(name = "duration_value")
    private Integer durationValue; // 月数，终身版为-1
    
    @Column(name = "price")
    private BigDecimal price;
    
    @Column(name = "original_price")
    private BigDecimal originalPrice;
    
    @Column(name = "currency")
    private String currency;
    
    @Column(name = "is_active")
    private Boolean isActive;
    
    @Column(name = "sort_order")
    private Integer sortOrder;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "features_json")
    private String featuresJson; // JSON格式的功能列表
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

### 4.2 用户订阅状态

```java
/**
 * 用户VIP订阅
 */
@Data
@Entity
@Table(name = "user_vip_subscriptions")
public class UserVipSubscription {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id")
    private Long userId;
    
    @Column(name = "plan_id")
    private String planId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "user_tier")
    private UserTier userTier;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private SubscriptionStatus status;
    
    @Column(name = "started_at")
    private LocalDateTime startedAt;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "auto_renew")
    private Boolean autoRenew;
    
    @Column(name = "payment_method")
    private String paymentMethod;
    
    @Column(name = "order_id")
    private String orderId;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}

/**
 * 订阅状态枚举
 */
public enum SubscriptionStatus {
    ACTIVE("有效"),
    EXPIRED("已过期"),
    CANCELLED("已取消"),
    SUSPENDED("已暂停"),
    PENDING("待激活");
    
    private final String displayName;
    
    SubscriptionStatus(String displayName) {
        this.displayName = displayName;
    }
}
```

## 5. 权限检查机制

### 5.1 权限检查服务

```java
/**
 * VIP权限检查服务
 */
@Service
public class VipPermissionService {
    
    @Autowired
    private UserVipSubscriptionService subscriptionService;
    
    @Autowired
    private VipPermissionConfigService configService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String PERMISSION_CACHE_KEY = "vip:permission:";
    private static final Duration CACHE_DURATION = Duration.ofHours(2);
    
    /**
     * 检查用户是否有指定权限
     */
    public boolean hasPermission(Long userId, PermissionType permissionType) {
        try {
            VipPermissionConfig config = getUserPermissionConfig(userId);
            PermissionValue permission = config.getPermissions().get(permissionType);
            
            return permission != null && permission.isEnabled();
            
        } catch (Exception e) {
            log.error("权限检查失败, userId: {}, permission: {}", userId, permissionType, e);
            return false; // 默认无权限，确保安全
        }
    }
    
    /**
     * 检查配额权限
     */
    public QuotaCheckResult checkQuota(Long userId, PermissionType quotaType, int requestAmount) {
        try {
            VipPermissionConfig config = getUserPermissionConfig(userId);
            PermissionValue permission = config.getPermissions().get(quotaType);
            
            if (permission == null || !permission.isEnabled()) {
                return QuotaCheckResult.denied("无此权限");
            }
            
            // 检查配额限制
            String quotaValue = permission.getValue();
            if ("unlimited".equals(quotaValue) || "-1".equals(quotaValue)) {
                return QuotaCheckResult.allowed();
            }
            
            int quotaLimit = Integer.parseInt(quotaValue);
            int currentUsage = getCurrentUsage(userId, quotaType);
            
            if (currentUsage + requestAmount <= quotaLimit) {
                return QuotaCheckResult.allowed();
            } else {
                return QuotaCheckResult.denied(
                    String.format("配额不足，当前使用: %d/%d", currentUsage, quotaLimit));
            }
            
        } catch (Exception e) {
            log.error("配额检查失败, userId: {}, quotaType: {}", userId, quotaType, e);
            return QuotaCheckResult.denied("系统错误");
        }
    }
    
    /**
     * 获取用户权限配置（带缓存）
     */
    private VipPermissionConfig getUserPermissionConfig(Long userId) {
        String cacheKey = PERMISSION_CACHE_KEY + userId;
        
        VipPermissionConfig cached = (VipPermissionConfig) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        VipPermissionConfig config = buildUserPermissionConfig(userId);
        redisTemplate.opsForValue().set(cacheKey, config, CACHE_DURATION);
        
        return config;
    }
    
    /**
     * 构建用户权限配置
     */
    private VipPermissionConfig buildUserPermissionConfig(Long userId) {
        UserVipSubscription subscription = subscriptionService.getActiveSubscription(userId);
        
        if (subscription == null || subscription.getStatus() != SubscriptionStatus.ACTIVE) {
            // 免费用户权限
            return configService.getFreeUserPermissionConfig();
        }
        
        // VIP用户权限
        return configService.getVipPermissionConfig(subscription.getUserTier());
    }
}

/**
 * 配额检查结果
 */
@Data
@Builder
public class QuotaCheckResult {
    private boolean allowed;
    private String message;
    private int currentUsage;
    private int quotaLimit;
    private int remainingQuota;

    public static QuotaCheckResult allowed() {
        return QuotaCheckResult.builder().allowed(true).build();
    }

    public static QuotaCheckResult denied(String message) {
        return QuotaCheckResult.builder().allowed(false).message(message).build();
    }
}

## 6. 与数据恢复功能的集成

### 6.1 数据恢复权限检查

```java
/**
 * 数据恢复功能的VIP权限集成
 */
@Service
public class DataRecoveryVipService {

    @Autowired
    private VipPermissionService vipPermissionService;

    /**
     * 检查数据恢复权限
     */
    public DataRecoveryPermissionResult checkDataRecoveryPermission(Long userId) {
        // 检查是否有数据恢复功能权限
        boolean hasFeature = vipPermissionService.hasPermission(userId,
            PermissionType.FEATURE_DATA_RECOVERY);

        if (!hasFeature) {
            return DataRecoveryPermissionResult.builder()
                .hasPermission(false)
                .reason("需要VIP权限")
                .upgradeRequired(true)
                .build();
        }

        // 检查恢复期限
        UserVipSubscription subscription = subscriptionService.getActiveSubscription(userId);
        if (subscription == null) {
            return DataRecoveryPermissionResult.builder()
                .hasPermission(false)
                .reason("VIP订阅已过期")
                .upgradeRequired(true)
                .build();
        }

        return DataRecoveryPermissionResult.builder()
            .hasPermission(true)
            .recoveryPeriodMonths(6)
            .subscriptionExpiresAt(subscription.getExpiresAt())
            .build();
    }

    /**
     * 检查批量恢复配额
     */
    public QuotaCheckResult checkBatchRecoveryQuota(Long userId, int itemCount) {
        // VIP用户批量恢复配额检查
        return vipPermissionService.checkQuota(userId,
            PermissionType.QUOTA_BATCH_RECOVERY_DAILY, itemCount);
    }
}

/**
 * 数据恢复权限检查结果
 */
@Data
@Builder
public class DataRecoveryPermissionResult {
    private boolean hasPermission;
    private String reason;
    private boolean upgradeRequired;
    private Integer recoveryPeriodMonths;
    private LocalDateTime subscriptionExpiresAt;
}
```

### 6.2 权限验证切面增强

```java
/**
 * VIP权限验证切面
 */
@Aspect
@Component
public class VipPermissionAspect {

    @Autowired
    private VipPermissionService vipPermissionService;

    /**
     * VIP功能权限注解
     */
    @Target({ElementType.METHOD})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface RequireVipPermission {
        PermissionType value();
        String message() default "此功能需要VIP权限";
        boolean checkQuota() default false;
        int quotaAmount() default 1;
    }

    /**
     * VIP权限验证
     */
    @Around("@annotation(requireVipPermission)")
    public Object checkVipPermission(ProceedingJoinPoint joinPoint,
                                   RequireVipPermission requireVipPermission) throws Throwable {

        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new VipPermissionException("用户未登录");
        }

        // 检查功能权限
        boolean hasPermission = vipPermissionService.hasPermission(userId,
            requireVipPermission.value());

        if (!hasPermission) {
            throw new VipPermissionException(requireVipPermission.message());
        }

        // 检查配额权限
        if (requireVipPermission.checkQuota()) {
            QuotaCheckResult quotaResult = vipPermissionService.checkQuota(userId,
                requireVipPermission.value(), requireVipPermission.quotaAmount());

            if (!quotaResult.isAllowed()) {
                throw new VipQuotaException(quotaResult.getMessage());
            }
        }

        return joinPoint.proceed();
    }
}
```

## 7. 系统集成架构

### 7.1 模块依赖关系

```
VIP体系核心模块
├── VIP权限管理 (vip-permission)
├── VIP订阅管理 (vip-subscription)
├── VIP计费系统 (vip-billing)
└── VIP配额管理 (vip-quota)

业务功能模块集成
├── 数据恢复功能 → VIP权限检查
├── 识图新建功能 → VIP权限检查 + 配额检查
├── 团队协作功能 → VIP权限检查 + 成员配额检查
├── 存储管理功能 → VIP存储配额检查
└── 广告控制功能 → VIP体验权限检查
```

### 7.2 事件驱动架构

```java
/**
 * VIP相关事件定义
 */
public class VipEvents {

    /**
     * VIP订阅激活事件
     */
    @Data
    @Builder
    public static class VipSubscriptionActivatedEvent {
        private Long userId;
        private UserTier userTier;
        private String planId;
        private LocalDateTime activatedAt;
        private LocalDateTime expiresAt;
    }

    /**
     * VIP订阅过期事件
     */
    @Data
    @Builder
    public static class VipSubscriptionExpiredEvent {
        private Long userId;
        private UserTier previousTier;
        private String planId;
        private LocalDateTime expiredAt;
    }

    /**
     * VIP权限变更事件
     */
    @Data
    @Builder
    public static class VipPermissionChangedEvent {
        private Long userId;
        private PermissionType permissionType;
        private PermissionValue oldValue;
        private PermissionValue newValue;
        private LocalDateTime changedAt;
    }
}

/**
 * VIP事件监听器
 */
@Component
public class VipEventListener {

    @Autowired
    private DataRecoveryService dataRecoveryService;

    @Autowired
    private UserNotificationService notificationService;

    /**
     * 处理VIP激活事件
     */
    @EventListener
    public void handleVipActivated(VipSubscriptionActivatedEvent event) {
        // 1. 清除权限缓存
        vipPermissionService.clearUserPermissionCache(event.getUserId());

        // 2. 统计可恢复数据（给用户惊喜）
        if (event.getUserTier().isVip()) {
            RecoverableDataStats stats = dataRecoveryService.getRecoverableDataStats(event.getUserId());
            if (stats.hasRecoverableData()) {
                notificationService.sendVipWelcomeNotification(event.getUserId(), stats);
            }
        }

        // 3. 记录用户行为日志
        userBehaviorService.logVipActivation(event);
    }

    /**
     * 处理VIP过期事件
     */
    @EventListener
    public void handleVipExpired(VipSubscriptionExpiredEvent event) {
        // 1. 清除权限缓存
        vipPermissionService.clearUserPermissionCache(event.getUserId());

        // 2. 发送续费提醒
        notificationService.sendRenewalReminder(event.getUserId());

        // 3. 优雅降级处理
        handleVipDowngrade(event.getUserId(), event.getPreviousTier());
    }
}
```
