# 数据恢复功能 - 权限控制与VIP验证

## 1. 权限体系设计

### 1.1 用户分层权限

```java
/**
 * 用户类型枚举
 */
public enum UserType {
    FREE("免费用户", 0),
    VIP("VIP用户", 1),
    ADMIN("管理员", 99);
    
    private final String description;
    private final int level;
    
    UserType(String description, int level) {
        this.description = description;
        this.level = level;
    }
}

/**
 * 数据恢复权限枚举
 */
public enum DataRecoveryPermission {
    NONE("无权限", 0, 0),
    SIX_MONTHS("6个月恢复", 6, 1),
    UNLIMITED("无限制恢复", -1, 99);
    
    private final String description;
    private final int months; // -1表示无限制
    private final int level;
    
    DataRecoveryPermission(String description, int months, int level) {
        this.description = description;
        this.months = months;
        this.level = level;
    }
}
```

### 1.2 权限配置表

```java
/**
 * 用户权限配置
 */
@Data
@Entity
@Table(name = "user_permissions")
public class UserPermission {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "permission_type", nullable = false, length = 50)
    private String permissionType;
    
    @Column(name = "permission_value", nullable = false, length = 100)
    private String permissionValue;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // 权限类型常量
    public static final String DATA_RECOVERY = "data_recovery";
    public static final String TEMPLATE_LIMIT = "template_limit";
    public static final String STORAGE_LIMIT = "storage_limit";
    public static final String TEAM_COLLABORATION = "team_collaboration";
}
```

## 2. 权限服务实现

### 2.1 用户权限服务

```java
/**
 * 用户权限服务
 */
@Service
@Transactional
public class UserPermissionService {
    
    @Autowired
    private UserPermissionMapper permissionMapper;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String PERMISSION_CACHE_KEY = "user:permission:";
    private static final int CACHE_EXPIRE_HOURS = 2;
    
    /**
     * 检查用户是否有数据恢复权限
     */
    public boolean hasDataRecoveryPermission(Long userId) {
        try {
            UserPermissionInfo permissionInfo = getUserPermissionInfo(userId);
            return permissionInfo.getDataRecoveryPermission() != DataRecoveryPermission.NONE;
        } catch (Exception e) {
            log.error("检查数据恢复权限失败, userId: {}", userId, e);
            return false;
        }
    }
    
    /**
     * 检查数据是否在恢复期限内
     */
    public boolean isWithinRecoveryPeriod(Long userId, LocalDateTime deletedAt) {
        if (deletedAt == null) {
            return false;
        }
        
        UserPermissionInfo permissionInfo = getUserPermissionInfo(userId);
        DataRecoveryPermission permission = permissionInfo.getDataRecoveryPermission();
        
        switch (permission) {
            case NONE:
                return false;
            case SIX_MONTHS:
                return deletedAt.isAfter(LocalDateTime.now().minusMonths(6));
            case UNLIMITED:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 获取用户权限信息（带缓存）
     */
    public UserPermissionInfo getUserPermissionInfo(Long userId) {
        String cacheKey = PERMISSION_CACHE_KEY + userId;
        
        // 先从缓存获取
        UserPermissionInfo cached = (UserPermissionInfo) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 从数据库查询
        UserPermissionInfo permissionInfo = buildUserPermissionInfo(userId);
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, permissionInfo, 
            Duration.ofHours(CACHE_EXPIRE_HOURS));
        
        return permissionInfo;
    }
    
    /**
     * 构建用户权限信息
     */
    private UserPermissionInfo buildUserPermissionInfo(Long userId) {
        User user = userService.getUserById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        List<UserPermission> permissions = permissionMapper.selectByUserId(userId);
        Map<String, UserPermission> permissionMap = permissions.stream()
            .collect(Collectors.toMap(UserPermission::getPermissionType, Function.identity()));
        
        UserPermissionInfo.Builder builder = UserPermissionInfo.builder()
            .userId(userId)
            .userType(user.getUserType());
        
        // 数据恢复权限
        UserPermission dataRecoveryPerm = permissionMap.get(UserPermission.DATA_RECOVERY);
        if (dataRecoveryPerm != null && isPermissionValid(dataRecoveryPerm)) {
            String value = dataRecoveryPerm.getPermissionValue();
            if ("6_months".equals(value) || "true".equals(value)) {
                builder.dataRecoveryPermission(DataRecoveryPermission.SIX_MONTHS);
            } else if ("unlimited".equals(value)) {
                builder.dataRecoveryPermission(DataRecoveryPermission.UNLIMITED);
            }
        } else {
            builder.dataRecoveryPermission(DataRecoveryPermission.NONE);
        }
        
        // 模板数量限制
        UserPermission templateLimitPerm = permissionMap.get(UserPermission.TEMPLATE_LIMIT);
        if (templateLimitPerm != null && isPermissionValid(templateLimitPerm)) {
            try {
                int limit = Integer.parseInt(templateLimitPerm.getPermissionValue());
                builder.templateLimit(limit);
            } catch (NumberFormatException e) {
                builder.templateLimit(100); // 默认限制
            }
        } else {
            builder.templateLimit(user.getUserType() == UserType.VIP ? -1 : 100);
        }
        
        return builder.build();
    }
    
    /**
     * 检查权限是否有效（未过期）
     */
    private boolean isPermissionValid(UserPermission permission) {
        return permission.getExpiresAt() == null || 
               permission.getExpiresAt().isAfter(LocalDateTime.now());
    }
    
    /**
     * 清除用户权限缓存
     */
    public void clearUserPermissionCache(Long userId) {
        String cacheKey = PERMISSION_CACHE_KEY + userId;
        redisTemplate.delete(cacheKey);
    }
}

/**
 * 用户权限信息
 */
@Data
@Builder
public class UserPermissionInfo {
    private Long userId;
    private UserType userType;
    private DataRecoveryPermission dataRecoveryPermission;
    private Integer templateLimit; // -1表示无限制
    private LocalDateTime permissionExpiresAt;
    
    /**
     * 是否为VIP用户
     */
    public boolean isVip() {
        return userType == UserType.VIP;
    }
    
    /**
     * 是否有数据恢复权限
     */
    public boolean hasDataRecovery() {
        return dataRecoveryPermission != DataRecoveryPermission.NONE;
    }
}
```

## 3. VIP权限验证

### 3.1 VIP状态检查

```java
/**
 * VIP服务
 */
@Service
public class VipService {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserPermissionService permissionService;
    
    @Autowired
    private VipOrderService vipOrderService;
    
    /**
     * 检查用户VIP状态
     */
    public VipStatus checkVipStatus(Long userId) {
        User user = userService.getUserById(userId);
        if (user == null) {
            return VipStatus.builder()
                .isVip(false)
                .status("USER_NOT_FOUND")
                .build();
        }
        
        // 检查当前VIP状态
        if (user.getUserType() != UserType.VIP) {
            return VipStatus.builder()
                .userId(userId)
                .isVip(false)
                .status("NOT_VIP")
                .message("用户不是VIP")
                .build();
        }
        
        // 检查VIP是否过期
        UserPermissionInfo permissionInfo = permissionService.getUserPermissionInfo(userId);
        LocalDateTime expiresAt = permissionInfo.getPermissionExpiresAt();
        
        if (expiresAt != null && expiresAt.isBefore(LocalDateTime.now())) {
            // VIP已过期，降级为免费用户
            downgradeToFreeUser(userId);
            return VipStatus.builder()
                .userId(userId)
                .isVip(false)
                .status("EXPIRED")
                .message("VIP已过期")
                .expiresAt(expiresAt)
                .build();
        }
        
        return VipStatus.builder()
            .userId(userId)
            .isVip(true)
            .status("ACTIVE")
            .message("VIP有效")
            .expiresAt(expiresAt)
            .build();
    }
    
    /**
     * 升级为VIP用户
     */
    @Transactional
    public void upgradeToVip(Long userId, VipUpgradeRequest request) {
        User user = userService.getUserById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        // 更新用户类型
        user.setUserType(UserType.VIP);
        user.setUpdatedAt(LocalDateTime.now());
        userService.updateUser(user);

        // 设置VIP权限
        LocalDateTime expiresAt = LocalDateTime.now().plus(request.getDuration());
        setVipPermissions(userId, expiresAt);

        // 清除权限缓存
        permissionService.clearUserPermissionCache(userId);

        // 重要：免费用户转VIP后，立即可以恢复6个月内的数据
        // 因为免费期间的数据也按6个月策略保留了
        log.info("用户升级为VIP成功, userId: {}, expiresAt: {}, 可恢复6个月内数据", userId, expiresAt);
    }
    
    /**
     * 降级为免费用户
     */
    @Transactional
    public void downgradeToFreeUser(Long userId) {
        User user = userService.getUserById(userId);
        if (user == null) {
            return;
        }
        
        // 更新用户类型
        user.setUserType(UserType.FREE);
        user.setUpdatedAt(LocalDateTime.now());
        userService.updateUser(user);
        
        // 设置免费用户权限
        setFreeUserPermissions(userId);
        
        // 清除权限缓存
        permissionService.clearUserPermissionCache(userId);
        
        log.info("用户降级为免费用户, userId: {}", userId);
    }
    
    /**
     * 设置VIP权限
     */
    private void setVipPermissions(Long userId, LocalDateTime expiresAt) {
        List<UserPermission> permissions = Arrays.asList(
            createPermission(userId, UserPermission.DATA_RECOVERY, "6_months", expiresAt),
            createPermission(userId, UserPermission.TEMPLATE_LIMIT, "unlimited", expiresAt),
            createPermission(userId, UserPermission.STORAGE_LIMIT, "unlimited", expiresAt),
            createPermission(userId, UserPermission.TEAM_COLLABORATION, "true", expiresAt)
        );
        
        permissionService.batchSaveOrUpdatePermissions(permissions);
    }
    
    /**
     * 设置免费用户权限
     */
    private void setFreeUserPermissions(Long userId) {
        List<UserPermission> permissions = Arrays.asList(
            createPermission(userId, UserPermission.DATA_RECOVERY, "false", null),
            createPermission(userId, UserPermission.TEMPLATE_LIMIT, "100", null),
            createPermission(userId, UserPermission.STORAGE_LIMIT, "1GB", null),
            createPermission(userId, UserPermission.TEAM_COLLABORATION, "false", null)
        );
        
        permissionService.batchSaveOrUpdatePermissions(permissions);
    }
    
    private UserPermission createPermission(Long userId, String type, String value, 
                                          LocalDateTime expiresAt) {
        UserPermission permission = new UserPermission();
        permission.setUserId(userId);
        permission.setPermissionType(type);
        permission.setPermissionValue(value);
        permission.setExpiresAt(expiresAt);
        permission.setCreatedAt(LocalDateTime.now());
        permission.setUpdatedAt(LocalDateTime.now());
        return permission;
    }
}

/**
 * VIP状态信息
 */
@Data
@Builder
public class VipStatus {
    private Long userId;
    private boolean isVip;
    private String status; // ACTIVE, EXPIRED, NOT_VIP, USER_NOT_FOUND
    private String message;
    private LocalDateTime expiresAt;
    private List<String> availableFeatures;
}

## 4. 权限验证切面

### 4.1 数据恢复权限切面

```java
/**
 * 数据恢复权限验证切面
 */
@Aspect
@Component
@Slf4j
public class DataRecoveryPermission {

    @Autowired
    private UserPermissionService permissionService;

    /**
     * 数据恢复权限注解
     */
    @Target({ElementType.METHOD})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface RequireDataRecoveryPermission {
        String message() default "需要VIP权限才能使用数据恢复功能";
    }

    /**
     * 权限验证切点
     */
    @Pointcut("@annotation(requireDataRecoveryPermission)")
    public void dataRecoveryPermissionPointcut(RequireDataRecoveryPermission requireDataRecoveryPermission) {}

    /**
     * 权限验证前置通知
     */
    @Before("dataRecoveryPermissionPointcut(requireDataRecoveryPermission)")
    public void checkDataRecoveryPermission(JoinPoint joinPoint,
                                          RequireDataRecoveryPermission requireDataRecoveryPermission) {

        // 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new DataRecoveryException(ErrorCodes.UNAUTHORIZED, "用户未登录");
        }

        // 检查数据恢复权限
        if (!permissionService.hasDataRecoveryPermission(userId)) {
            log.warn("用户无数据恢复权限, userId: {}, method: {}",
                userId, joinPoint.getSignature().getName());
            throw new DataRecoveryException(ErrorCodes.NO_RECOVERY_PERMISSION,
                requireDataRecoveryPermission.message());
        }

        log.debug("数据恢复权限验证通过, userId: {}", userId);
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        // 从请求上下文或SecurityContext获取用户ID
        HttpServletRequest request = ((ServletRequestAttributes)
            RequestContextHolder.currentRequestAttributes()).getRequest();
        return (Long) request.getAttribute("userId");
    }
}
```

### 4.2 使用权限注解

```java
/**
 * 数据恢复服务实现
 */
@Service
public class DataRecoveryServiceImpl implements DataRecoveryService {

    /**
     * 恢复数据（需要VIP权限）
     */
    @RequireDataRecoveryPermission(message = "数据恢复功能仅限VIP用户使用")
    @Override
    public RestoreResponse restoreData(String dataType, Long itemId, Long userId,
                                     boolean restoreToOriginal) {
        // 实现数据恢复逻辑
        return doRestoreData(dataType, itemId, userId, restoreToOriginal);
    }

    /**
     * 批量恢复数据（需要VIP权限）
     */
    @RequireDataRecoveryPermission(message = "批量数据恢复功能仅限VIP用户使用")
    @Override
    public BatchRestoreResponse batchRestoreData(List<RestoreItem> items, Long userId,
                                               boolean restoreToOriginal) {
        // 实现批量恢复逻辑
        return doBatchRestoreData(items, userId, restoreToOriginal);
    }
}
```

## 5. 权限升级提示

### 5.1 权限不足处理

```java
/**
 * 权限升级提示服务
 */
@Service
public class PermissionUpgradeService {

    @Autowired
    private UserPermissionService permissionService;

    @Autowired
    private VipService vipService;

    /**
     * 获取权限升级提示信息
     */
    public UpgradePromptInfo getUpgradePrompt(Long userId, String feature) {
        UserPermissionInfo permissionInfo = permissionService.getUserPermissionInfo(userId);

        if (permissionInfo.isVip()) {
            return null; // VIP用户无需提示
        }

        return buildUpgradePrompt(feature, permissionInfo);
    }

    /**
     * 构建升级提示信息
     */
    private UpgradePromptInfo buildUpgradePrompt(String feature, UserPermissionInfo permissionInfo) {
        UpgradePromptInfo.Builder builder = UpgradePromptInfo.builder();

        switch (feature) {
            case "data_recovery":
                builder.title("数据恢复功能")
                    .description("误删除的模板、图片、文件可以轻松找回，6个月内数据安全保障")
                    .benefits(Arrays.asList(
                        "6个月内数据恢复",
                        "支持模板、图片、文件恢复",
                        "批量恢复操作",
                        "恢复历史记录"
                    ))
                    .upgradeUrl("/vip/upgrade?feature=data_recovery");
                break;

            case "unlimited_templates":
                builder.title("无限模板存储")
                    .description("突破100个模板限制，无限制保存您的创意模板")
                    .benefits(Arrays.asList(
                        "无限制模板数量",
                        "云端同步存储",
                        "多设备访问",
                        "模板分类管理"
                    ))
                    .upgradeUrl("/vip/upgrade?feature=unlimited_templates");
                break;

            default:
                builder.title("VIP专享功能")
                    .description("升级VIP解锁更多高级功能")
                    .upgradeUrl("/vip/upgrade");
        }

        return builder.currentPlan("免费版")
            .recommendedPlan("VIP版")
            .build();
    }
}

/**
 * 升级提示信息
 */
@Data
@Builder
public class UpgradePromptInfo {
    private String title;
    private String description;
    private List<String> benefits;
    private String currentPlan;
    private String recommendedPlan;
    private String upgradeUrl;
    private boolean showUpgradeButton;
}
```

### 5.2 权限检查工具类

```java
/**
 * 权限检查工具类
 */
@Component
public class PermissionChecker {

    @Autowired
    private UserPermissionService permissionService;

    /**
     * 检查并抛出权限异常
     */
    public void checkDataRecoveryPermissionOrThrow(Long userId) {
        if (!permissionService.hasDataRecoveryPermission(userId)) {
            throw new DataRecoveryException(ErrorCodes.NO_RECOVERY_PERMISSION,
                "数据恢复功能需要VIP权限，请升级后使用");
        }
    }

    /**
     * 检查数据恢复时间范围
     */
    public void checkRecoveryPeriodOrThrow(Long userId, LocalDateTime deletedAt) {
        if (!permissionService.isWithinRecoveryPeriod(userId, deletedAt)) {
            UserPermissionInfo permissionInfo = permissionService.getUserPermissionInfo(userId);
            if (!permissionInfo.hasDataRecovery()) {
                throw new DataRecoveryException(ErrorCodes.NO_RECOVERY_PERMISSION,
                    "数据恢复功能需要VIP权限");
            } else {
                throw new DataRecoveryException(ErrorCodes.RECOVERY_EXPIRED,
                    "数据删除时间超过6个月，无法恢复");
            }
        }
    }

    /**
     * 检查模板数量限制
     */
    public void checkTemplateLimitOrThrow(Long userId, int currentCount) {
        UserPermissionInfo permissionInfo = permissionService.getUserPermissionInfo(userId);
        Integer limit = permissionInfo.getTemplateLimit();

        if (limit != null && limit > 0 && currentCount >= limit) {
            throw new BusinessException(ErrorCodes.TEMPLATE_LIMIT_EXCEEDED,
                String.format("模板数量已达上限(%d个)，请升级VIP获得无限制存储", limit));
        }
    }
}
```

## 6. 权限监控与审计

### 6.1 权限操作日志

```java
/**
 * 权限操作日志
 */
@Entity
@Table(name = "permission_audit_logs")
@Data
public class PermissionAuditLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "operation_type", nullable = false)
    private String operationType; // CHECK, GRANT, REVOKE, UPGRADE, DOWNGRADE

    @Column(name = "permission_type", nullable = false)
    private String permissionType;

    @Column(name = "old_value")
    private String oldValue;

    @Column(name = "new_value")
    private String newValue;

    @Column(name = "result", nullable = false)
    private String result; // SUCCESS, FAILED, DENIED

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "user_agent")
    private String userAgent;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
}

/**
 * 权限审计服务
 */
@Service
public class PermissionAuditService {

    @Autowired
    private PermissionAuditLogMapper auditLogMapper;

    /**
     * 记录权限检查日志
     */
    public void logPermissionCheck(Long userId, String permissionType, boolean hasPermission,
                                 HttpServletRequest request) {
        PermissionAuditLog log = new PermissionAuditLog();
        log.setUserId(userId);
        log.setOperationType("CHECK");
        log.setPermissionType(permissionType);
        log.setResult(hasPermission ? "SUCCESS" : "DENIED");
        log.setIpAddress(getClientIpAddress(request));
        log.setUserAgent(request.getHeader("User-Agent"));
        log.setCreatedAt(LocalDateTime.now());

        auditLogMapper.insert(log);
    }

    /**
     * 记录权限变更日志
     */
    public void logPermissionChange(Long userId, String permissionType, String oldValue,
                                  String newValue, String operationType) {
        PermissionAuditLog log = new PermissionAuditLog();
        log.setUserId(userId);
        log.setOperationType(operationType);
        log.setPermissionType(permissionType);
        log.setOldValue(oldValue);
        log.setNewValue(newValue);
        log.setResult("SUCCESS");
        log.setCreatedAt(LocalDateTime.now());

        auditLogMapper.insert(log);
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotEmpty(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
}
```
