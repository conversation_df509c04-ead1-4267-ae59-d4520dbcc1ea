# XPrinter VIP功能控制与限制

## 1. 功能控制架构

### 1.1 设计目标
- **精确控制**：对每个功能进行精确的VIP权限控制
- **配额管理**：灵活的配额分配和使用监控
- **使用统计**：详细的功能使用数据统计
- **降级保护**：VIP过期后的功能降级处理

### 1.2 功能分类
```
VIP功能控制体系
├── 核心功能控制 (Core Features)
│   ├── 数据恢复功能
│   ├── 识图新建功能
│   ├── 价签打印功能
│   └── 团队协作功能
├── 配额限制控制 (Quota Limits)
│   ├── 模板数量限制
│   ├── 存储空间限制
│   ├── OCR使用次数
│   └── 团队成员数量
└── 体验增强控制 (Experience)
    ├── 无广告体验
    ├── 优先客服支持
    └── Beta功能体验
```

## 2. 数据恢复功能控制

### 2.1 数据恢复权限控制器

```java
/**
 * 数据恢复功能控制器
 */
@Component
public class DataRecoveryFeatureController {
    
    @Autowired
    private VipPermissionService vipPermissionService;
    
    @Autowired
    private VipQuotaService quotaService;
    
    /**
     * 检查数据恢复功能权限
     */
    public FeatureAccessResult checkDataRecoveryAccess(Long userId) {
        try {
            // 1. 检查基础功能权限
            PermissionCheckResult permissionResult = vipPermissionService.checkFeaturePermission(
                userId, PermissionType.FEATURE_DATA_RECOVERY);
            
            if (!permissionResult.isAllowed()) {
                return FeatureAccessResult.denied(
                    "DATA_RECOVERY_VIP_REQUIRED", 
                    "数据恢复功能需要VIP权限",
                    buildUpgradeInfo());
            }
            
            // 2. 检查恢复期限
            UserVipSubscription subscription = subscriptionService.getActiveSubscription(userId);
            if (subscription == null) {
                return FeatureAccessResult.denied(
                    "VIP_SUBSCRIPTION_REQUIRED", 
                    "需要有效的VIP订阅");
            }
            
            // 3. 构建访问信息
            DataRecoveryAccessInfo accessInfo = DataRecoveryAccessInfo.builder()
                .hasAccess(true)
                .recoveryPeriodMonths(6)
                .subscriptionExpiresAt(subscription.getExpiresAt())
                .userTier(subscription.getUserTier())
                .build();
            
            return FeatureAccessResult.allowed(accessInfo);
            
        } catch (Exception e) {
            log.error("检查数据恢复权限失败, userId: {}", userId, e);
            return FeatureAccessResult.denied("SYSTEM_ERROR", "系统错误");
        }
    }
    
    /**
     * 检查批量恢复配额
     */
    public QuotaAccessResult checkBatchRecoveryQuota(Long userId, int requestCount) {
        try {
            // 检查每日批量恢复次数限制
            QuotaCheckResult quotaResult = quotaService.checkQuota(
                userId, PermissionType.QUOTA_BATCH_RECOVERY_DAILY, requestCount);
            
            if (!quotaResult.isAllowed()) {
                return QuotaAccessResult.denied(
                    "DAILY_BATCH_RECOVERY_LIMIT_EXCEEDED",
                    quotaResult.getMessage(),
                    quotaResult.getCurrentUsage(),
                    quotaResult.getQuotaLimit());
            }
            
            return QuotaAccessResult.allowed(
                quotaResult.getCurrentUsage(),
                quotaResult.getQuotaLimit(),
                quotaResult.getRemainingQuota());
            
        } catch (Exception e) {
            log.error("检查批量恢复配额失败, userId: {}", userId, e);
            return QuotaAccessResult.denied("SYSTEM_ERROR", "系统错误", 0, 0);
        }
    }
    
    /**
     * 记录数据恢复使用
     */
    public void recordDataRecoveryUsage(Long userId, DataRecoveryUsageType usageType, int count) {
        try {
            switch (usageType) {
                case SINGLE_RECOVERY:
                    // 单个恢复不计入配额
                    break;
                case BATCH_RECOVERY:
                    // 批量恢复计入每日配额
                    quotaService.consumeQuota(userId, 
                        PermissionType.QUOTA_BATCH_RECOVERY_DAILY, count);
                    break;
                case DATE_RANGE_RECOVERY:
                    // 按日期范围恢复计入每日配额
                    quotaService.consumeQuota(userId, 
                        PermissionType.QUOTA_BATCH_RECOVERY_DAILY, 1);
                    break;
            }
            
            // 记录使用统计
            usageStatisticsService.recordFeatureUsage(userId, 
                FeatureType.DATA_RECOVERY, usageType.name(), count);
            
        } catch (Exception e) {
            log.error("记录数据恢复使用失败, userId: {}, type: {}", userId, usageType, e);
        }
    }
    
    private UpgradeInfo buildUpgradeInfo() {
        return UpgradeInfo.builder()
            .title("解锁数据恢复功能")
            .description("误删除的数据可以轻松找回，6个月内数据安全保障")
            .benefits(Arrays.asList(
                "6个月内数据恢复",
                "支持模板、图片、文件恢复", 
                "批量恢复操作",
                "按日期范围恢复"
            ))
            .upgradeUrl("/vip/upgrade?feature=data_recovery")
            .build();
    }
}

/**
 * 数据恢复使用类型
 */
public enum DataRecoveryUsageType {
    SINGLE_RECOVERY("单个恢复"),
    BATCH_RECOVERY("批量恢复"),
    DATE_RANGE_RECOVERY("按日期范围恢复");
    
    private final String description;
    
    DataRecoveryUsageType(String description) {
        this.description = description;
    }
}
```

## 3. OCR识图功能控制

### 3.1 OCR功能控制器

```java
/**
 * OCR识图功能控制器
 */
@Component
public class OcrFeatureController {
    
    @Autowired
    private VipPermissionService vipPermissionService;
    
    @Autowired
    private VipQuotaService quotaService;
    
    /**
     * 检查OCR功能权限
     */
    public FeatureAccessResult checkOcrAccess(Long userId) {
        try {
            // 1. 检查功能权限
            PermissionCheckResult permissionResult = vipPermissionService.checkFeaturePermission(
                userId, PermissionType.FEATURE_OCR_CREATE);
            
            if (!permissionResult.isAllowed()) {
                return FeatureAccessResult.denied(
                    "OCR_VIP_REQUIRED",
                    "识图新建功能需要VIP权限",
                    buildOcrUpgradeInfo());
            }
            
            // 2. 检查月度配额
            QuotaCheckResult quotaResult = quotaService.checkQuota(
                userId, PermissionType.QUOTA_OCR_MONTHLY, 1);
            
            if (!quotaResult.isAllowed()) {
                return FeatureAccessResult.denied(
                    "OCR_MONTHLY_LIMIT_EXCEEDED",
                    String.format("本月OCR次数已用完 (%d/%d)", 
                        quotaResult.getCurrentUsage(), quotaResult.getQuotaLimit()));
            }
            
            // 3. 构建访问信息
            OcrAccessInfo accessInfo = OcrAccessInfo.builder()
                .hasAccess(true)
                .monthlyUsage(quotaResult.getCurrentUsage())
                .monthlyLimit(quotaResult.getQuotaLimit())
                .remainingCount(quotaResult.getRemainingQuota())
                .resetDate(getMonthlyResetDate())
                .build();
            
            return FeatureAccessResult.allowed(accessInfo);
            
        } catch (Exception e) {
            log.error("检查OCR权限失败, userId: {}", userId, e);
            return FeatureAccessResult.denied("SYSTEM_ERROR", "系统错误");
        }
    }
    
    /**
     * 消费OCR配额
     */
    public QuotaConsumeResult consumeOcrQuota(Long userId) {
        try {
            // 消费月度OCR配额
            QuotaConsumeResult result = quotaService.consumeQuota(
                userId, PermissionType.QUOTA_OCR_MONTHLY, 1);
            
            if (result.isSuccess()) {
                // 记录使用统计
                usageStatisticsService.recordFeatureUsage(userId, 
                    FeatureType.OCR_CREATE, "ocr_recognition", 1);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("消费OCR配额失败, userId: {}", userId, e);
            return QuotaConsumeResult.failed("配额消费失败");
        }
    }
    
    /**
     * 获取OCR使用统计
     */
    public OcrUsageStats getOcrUsageStats(Long userId) {
        try {
            // 获取当月使用情况
            LocalDate startOfMonth = LocalDate.now().withDayOfMonth(1);
            LocalDate endOfMonth = LocalDate.now().withDayOfMonth(
                LocalDate.now().lengthOfMonth());
            
            int monthlyUsage = usageStatisticsService.getFeatureUsageCount(
                userId, FeatureType.OCR_CREATE, startOfMonth, endOfMonth);
            
            // 获取配额限制
            UserPermissionContext context = vipPermissionService.getUserPermissionContext(userId);
            PermissionValue ocrPermission = context.getPermissions().get(PermissionType.QUOTA_OCR_MONTHLY);
            
            int monthlyLimit = parseQuotaLimit(ocrPermission);
            
            return OcrUsageStats.builder()
                .monthlyUsage(monthlyUsage)
                .monthlyLimit(monthlyLimit)
                .remainingCount(Math.max(0, monthlyLimit - monthlyUsage))
                .resetDate(getMonthlyResetDate())
                .dailyUsage(getDailyUsage(userId))
                .build();
            
        } catch (Exception e) {
            log.error("获取OCR使用统计失败, userId: {}", userId, e);
            return OcrUsageStats.empty();
        }
    }
    
    private UpgradeInfo buildOcrUpgradeInfo() {
        return UpgradeInfo.builder()
            .title("解锁识图新建功能")
            .description("拍照识别图片中的文字，快速创建打印模板")
            .benefits(Arrays.asList(
                "月度1000次OCR识别",
                "高精度文字识别",
                "支持多种图片格式",
                "智能版面分析"
            ))
            .upgradeUrl("/vip/upgrade?feature=ocr_create")
            .build();
    }
    
    private LocalDate getMonthlyResetDate() {
        return LocalDate.now().plusMonths(1).withDayOfMonth(1);
    }
    
    private int parseQuotaLimit(PermissionValue permission) {
        if (permission == null || !permission.isEnabled()) {
            return 0;
        }
        
        String value = permission.getValue();
        if ("-1".equals(value) || "unlimited".equals(value)) {
            return Integer.MAX_VALUE;
        }
        
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    private int getDailyUsage(Long userId) {
        LocalDate today = LocalDate.now();
        return usageStatisticsService.getFeatureUsageCount(
            userId, FeatureType.OCR_CREATE, today, today);
    }
}

/**
 * OCR访问信息
 */
@Data
@Builder
public class OcrAccessInfo {
    private boolean hasAccess;
    private int monthlyUsage;
    private int monthlyLimit;
    private int remainingCount;
    private LocalDate resetDate;
}

/**
 * OCR使用统计
 */
@Data
@Builder
public class OcrUsageStats {
    private int monthlyUsage;
    private int monthlyLimit;
    private int remainingCount;
    private LocalDate resetDate;
    private int dailyUsage;
    
    public static OcrUsageStats empty() {
        return OcrUsageStats.builder()
            .monthlyUsage(0)
            .monthlyLimit(0)
            .remainingCount(0)
            .dailyUsage(0)
            .build();
    }
}

## 4. 存储配额控制

### 4.1 存储配额控制器

```java
/**
 * 存储配额控制器
 */
@Component
public class StorageQuotaController {

    @Autowired
    private VipPermissionService vipPermissionService;

    @Autowired
    private StorageService storageService;

    /**
     * 检查模板数量限制
     */
    public QuotaAccessResult checkTemplateCountLimit(Long userId) {
        try {
            // 1. 获取当前模板数量
            int currentCount = templateService.getUserTemplateCount(userId);

            // 2. 获取配额限制
            UserPermissionContext context = vipPermissionService.getUserPermissionContext(userId);
            PermissionValue templateQuota = context.getPermissions().get(PermissionType.QUOTA_TEMPLATE_COUNT);

            int limit = parseQuotaLimit(templateQuota);

            if (limit == -1) {
                // 无限制
                return QuotaAccessResult.unlimited(currentCount);
            }

            if (currentCount >= limit) {
                return QuotaAccessResult.denied(
                    "TEMPLATE_COUNT_LIMIT_EXCEEDED",
                    String.format("模板数量已达上限 (%d/%d)", currentCount, limit),
                    currentCount, limit);
            }

            return QuotaAccessResult.allowed(currentCount, limit, limit - currentCount);

        } catch (Exception e) {
            log.error("检查模板数量限制失败, userId: {}", userId, e);
            return QuotaAccessResult.denied("SYSTEM_ERROR", "系统错误", 0, 0);
        }
    }

    /**
     * 检查存储空间限制
     */
    public StorageQuotaResult checkStorageSpaceLimit(Long userId, long additionalSize) {
        try {
            // 1. 获取当前存储使用量
            long currentUsage = storageService.getUserStorageUsage(userId);

            // 2. 获取存储配额
            UserPermissionContext context = vipPermissionService.getUserPermissionContext(userId);
            PermissionValue storageQuota = context.getPermissions().get(PermissionType.QUOTA_STORAGE_SIZE);

            long limit = parseStorageLimit(storageQuota);

            if (limit == -1) {
                // 无限制
                return StorageQuotaResult.unlimited(currentUsage);
            }

            if (currentUsage + additionalSize > limit) {
                return StorageQuotaResult.denied(
                    "STORAGE_LIMIT_EXCEEDED",
                    String.format("存储空间不足，当前使用: %s，限制: %s",
                        formatBytes(currentUsage), formatBytes(limit)),
                    currentUsage, limit);
            }

            return StorageQuotaResult.allowed(currentUsage, limit, limit - currentUsage);

        } catch (Exception e) {
            log.error("检查存储空间限制失败, userId: {}", userId, e);
            return StorageQuotaResult.denied("SYSTEM_ERROR", "系统错误", 0, 0);
        }
    }

    /**
     * 检查单文件大小限制
     */
    public FileSizeResult checkFileSizeLimit(Long userId, long fileSize) {
        try {
            UserPermissionContext context = vipPermissionService.getUserPermissionContext(userId);
            PermissionValue fileSizeQuota = context.getPermissions().get(PermissionType.QUOTA_FILE_SIZE_LIMIT);

            long limit = parseStorageLimit(fileSizeQuota);

            if (limit == -1) {
                return FileSizeResult.allowed(fileSize, -1);
            }

            if (fileSize > limit) {
                return FileSizeResult.denied(
                    "FILE_SIZE_LIMIT_EXCEEDED",
                    String.format("文件大小超出限制，当前: %s，限制: %s",
                        formatBytes(fileSize), formatBytes(limit)),
                    fileSize, limit);
            }

            return FileSizeResult.allowed(fileSize, limit);

        } catch (Exception e) {
            log.error("检查文件大小限制失败, userId: {}", userId, e);
            return FileSizeResult.denied("SYSTEM_ERROR", "系统错误", fileSize, 0);
        }
    }

    /**
     * 获取存储使用统计
     */
    public StorageUsageStats getStorageUsageStats(Long userId) {
        try {
            // 获取各类型文件使用量
            long templateStorage = storageService.getTemplateStorageUsage(userId);
            long imageStorage = storageService.getImageStorageUsage(userId);
            long documentStorage = storageService.getDocumentStorageUsage(userId);
            long totalUsage = templateStorage + imageStorage + documentStorage;

            // 获取存储限制
            UserPermissionContext context = vipPermissionService.getUserPermissionContext(userId);
            PermissionValue storageQuota = context.getPermissions().get(PermissionType.QUOTA_STORAGE_SIZE);
            long storageLimit = parseStorageLimit(storageQuota);

            // 获取文件数量
            int templateCount = templateService.getUserTemplateCount(userId);
            int imageCount = fileService.getUserImageCount(userId);
            int documentCount = fileService.getUserDocumentCount(userId);

            return StorageUsageStats.builder()
                .totalUsage(totalUsage)
                .storageLimit(storageLimit)
                .remainingStorage(storageLimit == -1 ? -1 : Math.max(0, storageLimit - totalUsage))
                .templateStorage(templateStorage)
                .imageStorage(imageStorage)
                .documentStorage(documentStorage)
                .templateCount(templateCount)
                .imageCount(imageCount)
                .documentCount(documentCount)
                .usagePercentage(storageLimit == -1 ? 0 : (double) totalUsage / storageLimit * 100)
                .build();

        } catch (Exception e) {
            log.error("获取存储使用统计失败, userId: {}", userId, e);
            return StorageUsageStats.empty();
        }
    }

    private int parseQuotaLimit(PermissionValue permission) {
        if (permission == null || !permission.isEnabled()) {
            return 0;
        }

        String value = permission.getValue();
        if ("-1".equals(value) || "unlimited".equals(value)) {
            return -1;
        }

        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    private long parseStorageLimit(PermissionValue permission) {
        if (permission == null || !permission.isEnabled()) {
            return 0;
        }

        String value = permission.getValue();
        if ("-1".equals(value) || "unlimited".equals(value)) {
            return -1;
        }

        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
}

/**
 * 存储配额结果
 */
@Data
@Builder
public class StorageQuotaResult {
    private boolean allowed;
    private String errorCode;
    private String message;
    private long currentUsage;
    private long storageLimit;
    private long remainingStorage;

    public static StorageQuotaResult allowed(long currentUsage, long limit, long remaining) {
        return StorageQuotaResult.builder()
            .allowed(true)
            .currentUsage(currentUsage)
            .storageLimit(limit)
            .remainingStorage(remaining)
            .build();
    }

    public static StorageQuotaResult unlimited(long currentUsage) {
        return StorageQuotaResult.builder()
            .allowed(true)
            .currentUsage(currentUsage)
            .storageLimit(-1)
            .remainingStorage(-1)
            .build();
    }

    public static StorageQuotaResult denied(String errorCode, String message, long currentUsage, long limit) {
        return StorageQuotaResult.builder()
            .allowed(false)
            .errorCode(errorCode)
            .message(message)
            .currentUsage(currentUsage)
            .storageLimit(limit)
            .remainingStorage(0)
            .build();
    }
}

/**
 * 存储使用统计
 */
@Data
@Builder
public class StorageUsageStats {
    private long totalUsage;
    private long storageLimit;
    private long remainingStorage;
    private long templateStorage;
    private long imageStorage;
    private long documentStorage;
    private int templateCount;
    private int imageCount;
    private int documentCount;
    private double usagePercentage;

    public static StorageUsageStats empty() {
        return StorageUsageStats.builder()
            .totalUsage(0)
            .storageLimit(0)
            .remainingStorage(0)
            .usagePercentage(0)
            .build();
    }
}
```
