# 免费用户转VIP策略 - 数据保留与恢复权限设计

## 1. 策略背景

### 1.1 产品策略考虑
免费用户可能在使用过程中转换为VIP用户，为了提升用户转换率和体验，我们采用**数据保留与权限分离**的策略：
- **数据保留**：免费用户和VIP用户采用相同的6个月保留策略
- **权限控制**：只有VIP用户才能使用数据恢复功能
- **转换友好**：免费用户转VIP后立即获得6个月内数据的恢复权限

### 1.2 业务价值
- 🚀 **提升转换率**：用户知道转VIP后能恢复历史数据
- 🛡️ **降低流失率**：误删除不再是用户流失的原因  
- 💎 **增强信任感**：用户感受到数据的安全保障
- 💰 **增加收入**：更多用户愿意为数据安全付费

## 2. 数据保留策略对比

### 2.1 旧策略（不推荐）
```
免费用户：
├── 删除后立即清理
└── 无数据恢复可能

VIP用户：
├── 0-6个月：可恢复
└── 6个月后：归档/清理

问题：
❌ 免费用户转VIP后无法恢复历史数据
❌ 用户体验差，转换意愿低
❌ 数据安全感不足
```

### 2.2 新策略（推荐）
```
所有用户统一保留策略：
├── 0-6个月：热存储保留
├── 6个月-1年：冷存储归档
└── 1年后：永久删除

权限差异化：
├── 免费用户：数据保留但无恢复权限
└── VIP用户：6个月内可恢复

优势：
✅ 转VIP后立即可恢复6个月内数据
✅ 用户体验好，转换意愿高
✅ 数据安全感强
✅ 技术实现简洁统一
```

## 3. 技术实现方案

### 3.1 数据库设计保持统一

```sql
-- 所有用户的数据表结构完全一致
CREATE TABLE `user_templates` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `template_name` varchar(255) NOT NULL,
  `template_data` longtext NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL, -- 软删除标记
  `deleted_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_deleted` (`user_id`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 不需要区分用户类型的特殊字段
-- 数据保留策略在应用层控制
```

### 3.2 权限控制实现

```java
/**
 * 数据恢复权限检查
 */
@Service
public class DataRecoveryPermissionService {
    
    /**
     * 检查用户是否有数据恢复权限
     */
    public boolean hasDataRecoveryPermission(Long userId) {
        UserPermissionInfo permissionInfo = getUserPermissionInfo(userId);
        
        // 只有VIP用户才有数据恢复权限
        return permissionInfo.getDataRecoveryPermission() != DataRecoveryPermission.NONE;
    }
    
    /**
     * 检查数据是否在恢复期限内
     */
    public boolean isWithinRecoveryPeriod(Long userId, LocalDateTime deletedAt) {
        if (deletedAt == null) return false;
        
        // 所有用户的数据都按6个月保留
        // 但只有VIP用户可以恢复
        boolean withinPeriod = deletedAt.isAfter(LocalDateTime.now().minusMonths(6));
        boolean hasPermission = hasDataRecoveryPermission(userId);
        
        return withinPeriod && hasPermission;
    }
}
```

### 3.3 VIP升级处理

```java
/**
 * VIP升级服务
 */
@Service
@Transactional
public class VipUpgradeService {
    
    /**
     * 用户升级为VIP
     */
    public VipUpgradeResult upgradeToVip(Long userId, VipUpgradeRequest request) {
        try {
            // 1. 更新用户类型
            User user = userService.getUserById(userId);
            user.setUserType(UserType.VIP);
            user.setUpdatedAt(LocalDateTime.now());
            userService.updateUser(user);
            
            // 2. 设置VIP权限
            LocalDateTime expiresAt = LocalDateTime.now().plus(request.getDuration());
            setVipPermissions(userId, expiresAt);
            
            // 3. 清除权限缓存，立即生效
            permissionService.clearUserPermissionCache(userId);
            
            // 4. 统计可恢复的数据数量（给用户惊喜）
            RecoverableDataStats stats = getRecoverableDataStats(userId);
            
            // 5. 记录升级日志
            logVipUpgrade(userId, stats);
            
            return VipUpgradeResult.success(stats);
            
        } catch (Exception e) {
            log.error("VIP升级失败, userId: {}", userId, e);
            return VipUpgradeResult.failure("升级失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户可恢复的数据统计
     */
    private RecoverableDataStats getRecoverableDataStats(Long userId) {
        LocalDateTime sixMonthsAgo = LocalDateTime.now().minusMonths(6);
        
        int recoverableTemplates = templateMapper.countDeletedSince(userId, sixMonthsAgo);
        int recoverableImages = fileMapper.countDeletedImagesSince(userId, sixMonthsAgo);
        int recoverableFiles = fileMapper.countDeletedFilesSince(userId, sixMonthsAgo);
        
        return RecoverableDataStats.builder()
            .templates(recoverableTemplates)
            .images(recoverableImages)
            .files(recoverableFiles)
            .totalCount(recoverableTemplates + recoverableImages + recoverableFiles)
            .build();
    }
}

/**
 * 可恢复数据统计
 */
@Data
@Builder
public class RecoverableDataStats {
    private int templates;
    private int images;
    private int files;
    private int totalCount;
    
    public boolean hasRecoverableData() {
        return totalCount > 0;
    }
}
```

## 4. 数据清理任务调整

### 4.1 统一的清理策略

```java
/**
 * 数据清理任务（不区分用户类型）
 */
@Component
public class UnifiedDataCleanupScheduler {
    
    /**
     * 每日数据归档任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyArchiveTask() {
        log.info("开始执行每日数据归档任务");
        
        try {
            // 归档所有用户6个月前的删除数据
            LocalDateTime cutoffTime = LocalDateTime.now().minusMonths(6);
            
            // 1. 归档模板数据
            List<UserTemplate> expiredTemplates = templateMapper.selectAllExpiredDeleted(cutoffTime);
            archiveTemplates(expiredTemplates);
            
            // 2. 归档文件数据
            List<CloudFile> expiredFiles = fileMapper.selectAllExpiredDeleted(cutoffTime);
            archiveFiles(expiredFiles);
            
            log.info("每日数据归档任务完成");
            
        } catch (Exception e) {
            log.error("每日数据归档任务失败", e);
        }
    }
    
    /**
     * 每月数据清理任务
     */
    @Scheduled(cron = "0 0 3 1 * ?")
    public void monthlyCleanupTask() {
        log.info("开始执行每月数据清理任务");
        
        try {
            // 清理所有用户1年前的归档数据
            LocalDateTime cutoffTime = LocalDateTime.now().minusYears(1);
            
            // 1. 清理数据库中的归档记录
            int deletedRecords = archiveRecordMapper.deleteOldRecords(cutoffTime);
            
            // 2. 清理OSS中的归档文件
            int deletedOssFiles = cleanupOssArchivedFiles(cutoffTime);
            
            log.info("每月数据清理任务完成, 清理记录: {}, OSS文件: {}", 
                deletedRecords, deletedOssFiles);
            
        } catch (Exception e) {
            log.error("每月数据清理任务失败", e);
        }
    }
}
```

## 5. 用户界面提示

### 5.1 免费用户的恢复提示

```java
/**
 * 免费用户访问数据恢复功能时的提示
 */
@RestController
public class DataRecoveryController {
    
    @GetMapping("/api/v1/data-recovery/deleted")
    public ApiResponse<PageResponse<DeletedDataItem>> getDeletedData(
            @RequestParam(defaultValue = "all") String dataType,
            HttpServletRequest request) {
        
        Long userId = getCurrentUserId(request);
        
        // 检查用户权限
        if (!permissionService.hasDataRecoveryPermission(userId)) {
            // 免费用户：显示可恢复数据数量，引导升级VIP
            RecoverableDataStats stats = getRecoverableDataStats(userId);
            
            return ApiResponse.error("NO_PERMISSION", 
                String.format("发现%d个可恢复数据，升级VIP立即恢复", stats.getTotalCount()))
                .setData(Map.of("recoverableStats", stats));
        }
        
        // VIP用户：正常返回数据
        PageResponse<DeletedDataItem> result = dataRecoveryService.queryDeletedData(queryParam);
        return ApiResponse.success(result);
    }
}
```

### 5.2 VIP升级成功提示

```json
{
  "code": 200,
  "message": "VIP升级成功！",
  "data": {
    "upgradeSuccess": true,
    "recoverableData": {
      "templates": 15,
      "images": 8,
      "files": 3,
      "totalCount": 26
    },
    "message": "恭喜！您现在可以恢复最近6个月内删除的26个数据项",
    "actionButton": {
      "text": "立即恢复数据",
      "action": "navigate_to_recovery"
    }
  }
}
```

## 6. 监控和分析

### 6.1 关键指标监控

```java
/**
 * 转换相关指标监控
 */
@Service
public class ConversionMetricsService {
    
    /**
     * 统计转换相关指标
     */
    public ConversionMetrics getConversionMetrics(LocalDate startDate, LocalDate endDate) {
        return ConversionMetrics.builder()
            // 转换率指标
            .freeToVipConversions(getFreeToVipConversions(startDate, endDate))
            .conversionRate(getConversionRate(startDate, endDate))
            
            // 数据恢复相关指标
            .recoveryAfterUpgrade(getRecoveryAfterUpgrade(startDate, endDate))
            .avgRecoverableDataCount(getAvgRecoverableDataCount(startDate, endDate))
            
            // 用户行为指标
            .deletionBeforeUpgrade(getDeletionBeforeUpgrade(startDate, endDate))
            .recoveryWithin24Hours(getRecoveryWithin24Hours(startDate, endDate))
            
            .build();
    }
}
```

### 6.2 A/B测试支持

```java
/**
 * 支持不同策略的A/B测试
 */
@Service
public class DataRetentionStrategyService {
    
    /**
     * 根据A/B测试分组决定数据保留策略
     */
    public DataRetentionStrategy getRetentionStrategy(Long userId) {
        String abGroup = abTestService.getUserGroup(userId, "data_retention_strategy");
        
        switch (abGroup) {
            case "immediate_cleanup":
                return DataRetentionStrategy.IMMEDIATE_CLEANUP; // 旧策略
            case "unified_retention":
                return DataRetentionStrategy.UNIFIED_RETENTION; // 新策略
            default:
                return DataRetentionStrategy.UNIFIED_RETENTION; // 默认新策略
        }
    }
}
```

---

这个策略变更是一个重要的产品决策，它平衡了成本控制和用户体验，为免费用户转VIP提供了强有力的激励机制。
