# XPrinter VIP权限管理系统

## 1. 权限管理架构

### 1.1 设计目标
- **高性能**：支持高并发权限检查，响应时间<50ms
- **灵活配置**：支持动态权限配置，无需重启服务
- **缓存优化**：多层缓存策略，减少数据库查询
- **扩展性强**：支持新增权限类型和用户等级

### 1.2 核心组件
```
VIP权限管理系统
├── 权限定义模块 (Permission Definition)
├── 权限检查引擎 (Permission Check Engine)
├── 权限缓存管理 (Permission Cache Manager)
├── 配额管理模块 (Quota Management)
└── 权限审计模块 (Permission Audit)
```

## 2. 权限定义与配置

### 2.1 权限类型定义

```java
/**
 * 权限类型枚举
 */
public enum PermissionType {
    // 数据恢复相关权限
    FEATURE_DATA_RECOVERY("数据恢复功能", PermissionCategory.FEATURE, "data_recovery"),
    QUOTA_BATCH_RECOVERY_DAILY("每日批量恢复次数", PermissionCategory.QUOTA, "batch_recovery_daily"),
    
    // OCR识图相关权限
    FEATURE_OCR_CREATE("识图新建功能", PermissionCategory.FEATURE, "ocr_create"),
    QUOTA_OCR_MONTHLY("月度OCR次数", PermissionCategory.QUOTA, "ocr_monthly"),
    
    // 存储相关权限
    QUOTA_TEMPLATE_COUNT("模板数量限制", PermissionCategory.QUOTA, "template_count"),
    QUOTA_STORAGE_SIZE("存储空间限制", PermissionCategory.QUOTA, "storage_size"),
    QUOTA_FILE_SIZE_LIMIT("单文件大小限制", PermissionCategory.QUOTA, "file_size_limit"),
    
    // 团队协作权限
    FEATURE_TEAM_COLLABORATION("团队协作功能", PermissionCategory.FEATURE, "team_collaboration"),
    QUOTA_TEAM_MEMBERS("团队成员数量", PermissionCategory.QUOTA, "team_members"),
    
    // 体验相关权限
    EXPERIENCE_AD_FREE("无广告体验", PermissionCategory.EXPERIENCE, "ad_free"),
    EXPERIENCE_PRIORITY_SUPPORT("优先客服", PermissionCategory.EXPERIENCE, "priority_support"),
    EXPERIENCE_BETA_FEATURES("Beta功能体验", PermissionCategory.EXPERIENCE, "beta_features");
    
    private final String displayName;
    private final PermissionCategory category;
    private final String code;
    
    PermissionType(String displayName, PermissionCategory category, String code) {
        this.displayName = displayName;
        this.category = category;
        this.code = code;
    }
}

/**
 * 权限分类
 */
public enum PermissionCategory {
    FEATURE("功能权限"),
    QUOTA("配额权限"),
    EXPERIENCE("体验权限");
    
    private final String displayName;
    
    PermissionCategory(String displayName) {
        this.displayName = displayName;
    }
}
```

### 2.2 权限配置服务

```java
/**
 * VIP权限配置服务
 */
@Service
public class VipPermissionConfigService {
    
    @Autowired
    private VipPermissionConfigMapper configMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CONFIG_CACHE_KEY = "vip:config:";
    private static final Duration CONFIG_CACHE_DURATION = Duration.ofHours(6);
    
    /**
     * 获取用户等级的权限配置
     */
    public VipPermissionConfig getPermissionConfig(UserTier userTier) {
        String cacheKey = CONFIG_CACHE_KEY + userTier.name();
        
        VipPermissionConfig cached = (VipPermissionConfig) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        VipPermissionConfig config = buildPermissionConfig(userTier);
        redisTemplate.opsForValue().set(cacheKey, config, CONFIG_CACHE_DURATION);
        
        return config;
    }
    
    /**
     * 构建权限配置
     */
    private VipPermissionConfig buildPermissionConfig(UserTier userTier) {
        List<VipPermissionConfigEntity> configEntities = 
            configMapper.selectByUserTier(userTier.name());
        
        Map<PermissionType, PermissionValue> permissions = new HashMap<>();
        
        for (VipPermissionConfigEntity entity : configEntities) {
            PermissionType permissionType = PermissionType.valueOf(entity.getPermissionType());
            
            PermissionValue value = PermissionValue.builder()
                .enabled(entity.getIsEnabled())
                .value(entity.getPermissionValue())
                .metadata(parseMetadata(entity.getMetadataJson()))
                .build();
            
            permissions.put(permissionType, value);
        }
        
        return VipPermissionConfig.builder()
            .userTier(userTier)
            .permissions(permissions)
            .effectiveDate(LocalDateTime.now())
            .build();
    }
    
    /**
     * 动态更新权限配置
     */
    @Transactional
    public void updatePermissionConfig(UserTier userTier, PermissionType permissionType, 
                                     PermissionValue newValue) {
        // 1. 更新数据库
        VipPermissionConfigEntity entity = configMapper.selectByTierAndType(
            userTier.name(), permissionType.name());
        
        if (entity != null) {
            entity.setIsEnabled(newValue.isEnabled());
            entity.setPermissionValue(newValue.getValue());
            entity.setMetadataJson(toJson(newValue.getMetadata()));
            entity.setUpdatedAt(LocalDateTime.now());
            configMapper.updateById(entity);
        }
        
        // 2. 清除缓存
        clearPermissionConfigCache(userTier);
        
        // 3. 发布配置变更事件
        publishPermissionConfigChangedEvent(userTier, permissionType, newValue);
    }
    
    /**
     * 清除权限配置缓存
     */
    public void clearPermissionConfigCache(UserTier userTier) {
        String cacheKey = CONFIG_CACHE_KEY + userTier.name();
        redisTemplate.delete(cacheKey);
    }
}
```

## 3. 权限检查引擎

### 3.1 核心权限检查服务

```java
/**
 * VIP权限检查引擎
 */
@Service
public class VipPermissionCheckEngine {
    
    @Autowired
    private VipPermissionConfigService configService;
    
    @Autowired
    private UserVipSubscriptionService subscriptionService;
    
    @Autowired
    private VipQuotaService quotaService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String USER_PERMISSION_CACHE_KEY = "vip:user_permission:";
    private static final Duration USER_PERMISSION_CACHE_DURATION = Duration.ofMinutes(30);
    
    /**
     * 检查功能权限
     */
    public PermissionCheckResult checkFeaturePermission(Long userId, PermissionType permissionType) {
        try {
            // 1. 参数验证
            if (permissionType.getCategory() != PermissionCategory.FEATURE) {
                return PermissionCheckResult.denied("权限类型错误");
            }
            
            // 2. 获取用户权限配置
            UserPermissionContext context = getUserPermissionContext(userId);
            if (context == null) {
                return PermissionCheckResult.denied("用户权限配置不存在");
            }
            
            // 3. 检查权限
            PermissionValue permission = context.getPermissions().get(permissionType);
            if (permission == null || !permission.isEnabled()) {
                return PermissionCheckResult.denied("无此功能权限");
            }
            
            // 4. 检查订阅状态
            if (!context.isSubscriptionActive()) {
                return PermissionCheckResult.denied("VIP订阅已过期");
            }
            
            return PermissionCheckResult.allowed();
            
        } catch (Exception e) {
            log.error("功能权限检查失败, userId: {}, permission: {}", userId, permissionType, e);
            return PermissionCheckResult.denied("系统错误");
        }
    }
    
    /**
     * 检查配额权限
     */
    public QuotaCheckResult checkQuotaPermission(Long userId, PermissionType permissionType, 
                                               int requestAmount) {
        try {
            // 1. 参数验证
            if (permissionType.getCategory() != PermissionCategory.QUOTA) {
                return QuotaCheckResult.denied("权限类型错误");
            }
            
            // 2. 获取用户权限配置
            UserPermissionContext context = getUserPermissionContext(userId);
            if (context == null || !context.isSubscriptionActive()) {
                return QuotaCheckResult.denied("VIP权限不足");
            }
            
            // 3. 获取配额配置
            PermissionValue permission = context.getPermissions().get(permissionType);
            if (permission == null || !permission.isEnabled()) {
                return QuotaCheckResult.denied("无此配额权限");
            }
            
            // 4. 检查配额限制
            return quotaService.checkQuota(userId, permissionType, permission, requestAmount);
            
        } catch (Exception e) {
            log.error("配额权限检查失败, userId: {}, permission: {}", userId, permissionType, e);
            return QuotaCheckResult.denied("系统错误");
        }
    }
    
    /**
     * 批量权限检查
     */
    public Map<PermissionType, PermissionCheckResult> batchCheckPermissions(
            Long userId, List<PermissionType> permissionTypes) {
        
        Map<PermissionType, PermissionCheckResult> results = new HashMap<>();
        
        // 获取用户权限上下文（一次查询）
        UserPermissionContext context = getUserPermissionContext(userId);
        
        for (PermissionType permissionType : permissionTypes) {
            if (context == null || !context.isSubscriptionActive()) {
                results.put(permissionType, PermissionCheckResult.denied("VIP权限不足"));
                continue;
            }
            
            PermissionValue permission = context.getPermissions().get(permissionType);
            if (permission == null || !permission.isEnabled()) {
                results.put(permissionType, PermissionCheckResult.denied("无此权限"));
            } else {
                results.put(permissionType, PermissionCheckResult.allowed());
            }
        }
        
        return results;
    }
    
    /**
     * 获取用户权限上下文（带缓存）
     */
    private UserPermissionContext getUserPermissionContext(Long userId) {
        String cacheKey = USER_PERMISSION_CACHE_KEY + userId;
        
        UserPermissionContext cached = (UserPermissionContext) 
            redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        UserPermissionContext context = buildUserPermissionContext(userId);
        if (context != null) {
            redisTemplate.opsForValue().set(cacheKey, context, USER_PERMISSION_CACHE_DURATION);
        }
        
        return context;
    }
    
    /**
     * 构建用户权限上下文
     */
    private UserPermissionContext buildUserPermissionContext(Long userId) {
        // 1. 获取用户订阅信息
        UserVipSubscription subscription = subscriptionService.getActiveSubscription(userId);
        UserTier userTier = subscription != null ? subscription.getUserTier() : UserTier.FREE;
        
        // 2. 获取权限配置
        VipPermissionConfig config = configService.getPermissionConfig(userTier);
        
        // 3. 构建上下文
        return UserPermissionContext.builder()
            .userId(userId)
            .userTier(userTier)
            .permissions(config.getPermissions())
            .subscription(subscription)
            .subscriptionActive(subscription != null && 
                subscription.getStatus() == SubscriptionStatus.ACTIVE &&
                (subscription.getExpiresAt() == null || 
                 subscription.getExpiresAt().isAfter(LocalDateTime.now())))
            .build();
    }
    
    /**
     * 清除用户权限缓存
     */
    public void clearUserPermissionCache(Long userId) {
        String cacheKey = USER_PERMISSION_CACHE_KEY + userId;
        redisTemplate.delete(cacheKey);
    }
}

/**
 * 用户权限上下文
 */
@Data
@Builder
public class UserPermissionContext {
    private Long userId;
    private UserTier userTier;
    private Map<PermissionType, PermissionValue> permissions;
    private UserVipSubscription subscription;
    private boolean subscriptionActive;
    private LocalDateTime cacheTime;
}

/**
 * 权限检查结果
 */
@Data
@Builder
public class PermissionCheckResult {
    private boolean allowed;
    private String message;
    private String errorCode;
    private Map<String, Object> metadata;

    public static PermissionCheckResult allowed() {
        return PermissionCheckResult.builder().allowed(true).build();
    }

    public static PermissionCheckResult denied(String message) {
        return PermissionCheckResult.builder()
            .allowed(false)
            .message(message)
            .build();
    }

    public static PermissionCheckResult denied(String errorCode, String message) {
        return PermissionCheckResult.builder()
            .allowed(false)
            .errorCode(errorCode)
            .message(message)
            .build();
    }
}

## 4. 配额管理模块

### 4.1 配额服务实现

```java
/**
 * VIP配额管理服务
 */
@Service
public class VipQuotaService {

    @Autowired
    private UserPermissionUsageMapper usageMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String QUOTA_CACHE_KEY = "vip:quota:";
    private static final Duration QUOTA_CACHE_DURATION = Duration.ofMinutes(10);

    /**
     * 检查配额
     */
    public QuotaCheckResult checkQuota(Long userId, PermissionType permissionType,
                                     PermissionValue permission, int requestAmount) {
        try {
            // 1. 解析配额限制
            QuotaLimit quotaLimit = parseQuotaLimit(permission);
            if (quotaLimit.isUnlimited()) {
                return QuotaCheckResult.allowed();
            }

            // 2. 获取当前使用量
            int currentUsage = getCurrentUsage(userId, permissionType, quotaLimit.getResetCycle());

            // 3. 检查是否超出限制
            if (currentUsage + requestAmount <= quotaLimit.getLimit()) {
                return QuotaCheckResult.builder()
                    .allowed(true)
                    .currentUsage(currentUsage)
                    .quotaLimit(quotaLimit.getLimit())
                    .remainingQuota(quotaLimit.getLimit() - currentUsage)
                    .build();
            } else {
                return QuotaCheckResult.builder()
                    .allowed(false)
                    .message(String.format("配额不足，当前使用: %d/%d", currentUsage, quotaLimit.getLimit()))
                    .currentUsage(currentUsage)
                    .quotaLimit(quotaLimit.getLimit())
                    .remainingQuota(0)
                    .build();
            }

        } catch (Exception e) {
            log.error("配额检查失败, userId: {}, permission: {}", userId, permissionType, e);
            return QuotaCheckResult.denied("配额检查失败");
        }
    }

    /**
     * 消费配额
     */
    @Transactional
    public QuotaConsumeResult consumeQuota(Long userId, PermissionType permissionType,
                                         int consumeAmount) {
        try {
            // 1. 先检查配额
            UserPermissionContext context = getUserPermissionContext(userId);
            PermissionValue permission = context.getPermissions().get(permissionType);

            QuotaCheckResult checkResult = checkQuota(userId, permissionType, permission, consumeAmount);
            if (!checkResult.isAllowed()) {
                return QuotaConsumeResult.failed(checkResult.getMessage());
            }

            // 2. 消费配额
            QuotaLimit quotaLimit = parseQuotaLimit(permission);
            LocalDate usageDate = LocalDate.now();

            UserPermissionUsage usage = usageMapper.selectByUserAndTypeAndDate(
                userId, permissionType.name(), usageDate);

            if (usage == null) {
                // 创建新的使用记录
                usage = new UserPermissionUsage();
                usage.setUserId(userId);
                usage.setPermissionType(permissionType.name());
                usage.setUsageDate(usageDate);
                usage.setUsageCount(consumeAmount);
                usage.setQuotaLimit(quotaLimit.getLimit());
                usage.setResetCycle(quotaLimit.getResetCycle().name());
                usage.setCreatedAt(LocalDateTime.now());
                usage.setUpdatedAt(LocalDateTime.now());
                usageMapper.insert(usage);
            } else {
                // 更新使用记录
                usage.setUsageCount(usage.getUsageCount() + consumeAmount);
                usage.setUpdatedAt(LocalDateTime.now());
                usageMapper.updateById(usage);
            }

            // 3. 清除缓存
            clearQuotaCache(userId, permissionType);

            return QuotaConsumeResult.success(usage.getUsageCount(), quotaLimit.getLimit());

        } catch (Exception e) {
            log.error("配额消费失败, userId: {}, permission: {}", userId, permissionType, e);
            return QuotaConsumeResult.failed("配额消费失败");
        }
    }

    /**
     * 获取当前使用量
     */
    private int getCurrentUsage(Long userId, PermissionType permissionType, ResetCycle resetCycle) {
        String cacheKey = QUOTA_CACHE_KEY + userId + ":" + permissionType.name();

        Integer cached = (Integer) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }

        LocalDate startDate = getResetStartDate(resetCycle);
        int usage = usageMapper.sumUsageByUserAndTypeAndDateRange(
            userId, permissionType.name(), startDate, LocalDate.now());

        redisTemplate.opsForValue().set(cacheKey, usage, QUOTA_CACHE_DURATION);
        return usage;
    }

    /**
     * 解析配额限制
     */
    private QuotaLimit parseQuotaLimit(PermissionValue permission) {
        String value = permission.getValue();
        Map<String, Object> metadata = permission.getMetadata();

        if ("-1".equals(value) || "unlimited".equals(value)) {
            return QuotaLimit.unlimited();
        }

        int limit = Integer.parseInt(value);
        String resetCycleStr = (String) metadata.getOrDefault("reset_cycle", "MONTHLY");
        ResetCycle resetCycle = ResetCycle.valueOf(resetCycleStr.toUpperCase());

        return QuotaLimit.builder()
            .limit(limit)
            .resetCycle(resetCycle)
            .unlimited(false)
            .build();
    }

    /**
     * 获取重置周期的开始日期
     */
    private LocalDate getResetStartDate(ResetCycle resetCycle) {
        LocalDate now = LocalDate.now();
        switch (resetCycle) {
            case DAILY:
                return now;
            case MONTHLY:
                return now.withDayOfMonth(1);
            case YEARLY:
                return now.withDayOfYear(1);
            default:
                return now;
        }
    }

    /**
     * 清除配额缓存
     */
    public void clearQuotaCache(Long userId, PermissionType permissionType) {
        String cacheKey = QUOTA_CACHE_KEY + userId + ":" + permissionType.name();
        redisTemplate.delete(cacheKey);
    }
}

/**
 * 配额限制
 */
@Data
@Builder
public class QuotaLimit {
    private int limit;
    private ResetCycle resetCycle;
    private boolean unlimited;

    public static QuotaLimit unlimited() {
        return QuotaLimit.builder().unlimited(true).build();
    }
}

/**
 * 重置周期
 */
public enum ResetCycle {
    DAILY, MONTHLY, YEARLY
}

/**
 * 配额消费结果
 */
@Data
@Builder
public class QuotaConsumeResult {
    private boolean success;
    private String message;
    private int currentUsage;
    private int quotaLimit;

    public static QuotaConsumeResult success(int currentUsage, int quotaLimit) {
        return QuotaConsumeResult.builder()
            .success(true)
            .currentUsage(currentUsage)
            .quotaLimit(quotaLimit)
            .build();
    }

    public static QuotaConsumeResult failed(String message) {
        return QuotaConsumeResult.builder()
            .success(false)
            .message(message)
            .build();
    }
}
```
