# 数据恢复功能 - API接口设计

## 1. 接口设计原则

### 1.1 RESTful设计
- 遵循REST规范，使用标准HTTP方法
- 统一的响应格式和错误码
- 支持分页和过滤查询

### 1.2 安全性
- 所有接口需要用户认证
- VIP权限验证
- 防止越权访问

### 1.3 性能考虑
- 支持批量操作减少网络请求
- 合理的分页大小
- 缓存优化

## 2. 通用响应格式

### 2.1 标准响应结构

```java
/**
 * 统一API响应格式
 */
@Data
public class ApiResponse<T> {
    private int code;
    private String message;
    private T data;
    private long timestamp;
    
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(200);
        response.setMessage("success");
        response.setData(data);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    public static <T> ApiResponse<T> error(String errorCode, String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(Integer.parseInt(errorCode));
        response.setMessage(message);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
}

/**
 * 分页响应格式
 */
@Data
public class PageResponse<T> {
    private List<T> items;
    private long total;
    private int page;
    private int size;
    private boolean hasNext;
}
```

### 2.2 错误码定义

```java
public class ErrorCodes {
    // 通用错误码
    public static final String SUCCESS = "200";
    public static final String BAD_REQUEST = "400";
    public static final String UNAUTHORIZED = "401";
    public static final String FORBIDDEN = "403";
    public static final String NOT_FOUND = "404";
    public static final String INTERNAL_ERROR = "500";
    
    // 数据恢复相关错误码
    public static final String NO_RECOVERY_PERMISSION = "4001";
    public static final String RECOVERY_EXPIRED = "4002";
    public static final String ALREADY_DELETED = "4003";
    public static final String ALREADY_RESTORED = "4004";
    public static final String DATA_NOT_FOUND = "4005";
    public static final String BATCH_OPERATION_FAILED = "4006";
}
```

## 3. 数据恢复核心接口

### 3.1 查询已删除数据接口

```java
/**
 * 数据恢复控制器
 */
@RestController
@RequestMapping("/api/v1/data-recovery")
@Validated
public class DataRecoveryController {
    
    @Autowired
    private DataRecoveryService dataRecoveryService;
    
    /**
     * 查询已删除的数据
     * GET /api/v1/data-recovery/deleted
     */
    @GetMapping("/deleted")
    public ApiResponse<PageResponse<DeletedDataItem>> getDeletedData(
            @RequestParam(defaultValue = "all") String dataType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size,
            HttpServletRequest request) {
        
        Long userId = getCurrentUserId(request);
        
        DeletedDataQueryParam queryParam = DeletedDataQueryParam.builder()
            .userId(userId)
            .dataType(dataType)
            .startDate(parseDate(startDate))
            .endDate(parseDate(endDate))
            .page(page)
            .size(size)
            .build();
        
        PageResponse<DeletedDataItem> result = dataRecoveryService.queryDeletedData(queryParam);
        return ApiResponse.success(result);
    }
}

/**
 * 查询参数
 */
@Data
@Builder
public class DeletedDataQueryParam {
    private Long userId;
    private String dataType; // template, image, file, all
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private int page;
    private int size;
}

/**
 * 已删除数据项
 */
@Data
public class DeletedDataItem {
    private Long id;
    private String name;
    private String type; // template, image, file
    private LocalDateTime createdTime;
    private LocalDateTime deletedTime;
    private String thumbnailUrl;
    private boolean canRecover;
    private String originalPosition;
    private Map<String, Object> metadata;
}
```

### 3.2 数据恢复接口

```java
/**
 * 恢复单个数据项
 * POST /api/v1/data-recovery/restore
 */
@PostMapping("/restore")
public ApiResponse<RestoreResponse> restoreData(
        @RequestBody @Valid RestoreRequest request,
        HttpServletRequest httpRequest) {
    
    Long userId = getCurrentUserId(httpRequest);
    
    RestoreResponse response = dataRecoveryService.restoreData(
        request.getDataType(), 
        request.getItemId(), 
        userId,
        request.isRestoreToOriginal()
    );
    
    return ApiResponse.success(response);
}

/**
 * 批量恢复数据
 * POST /api/v1/data-recovery/batch-restore
 */
@PostMapping("/batch-restore")
public ApiResponse<BatchRestoreResponse> batchRestoreData(
        @RequestBody @Valid BatchRestoreRequest request,
        HttpServletRequest httpRequest) {
    
    Long userId = getCurrentUserId(httpRequest);
    
    BatchRestoreResponse response = dataRecoveryService.batchRestoreData(
        request.getItems(), 
        userId,
        request.isRestoreToOriginal()
    );
    
    return ApiResponse.success(response);
}

/**
 * 恢复请求参数
 */
@Data
@Valid
public class RestoreRequest {
    @NotNull
    private String dataType; // template, image, file
    
    @NotNull
    private Long itemId;
    
    private boolean restoreToOriginal = true;
}

/**
 * 批量恢复请求参数
 */
@Data
@Valid
public class BatchRestoreRequest {
    @NotEmpty
    @Size(max = 50, message = "单次最多恢复50个项目")
    private List<RestoreItem> items;
    
    private boolean restoreToOriginal = true;
    
    @Data
    public static class RestoreItem {
        @NotNull
        private String dataType;
        
        @NotNull
        private Long itemId;
    }
}

/**
 * 恢复响应
 */
@Data
public class RestoreResponse {
    private boolean success;
    private String message;
    private Long itemId;
    private String dataType;
    private LocalDateTime restoredAt;
}

/**
 * 批量恢复响应
 */
@Data
public class BatchRestoreResponse {
    private int totalCount;
    private int successCount;
    private int failureCount;
    private List<RestoreResponse> results;
    private List<String> errorMessages;
}
```

### 3.3 权限检查接口

```java
/**
 * 检查数据恢复权限
 * GET /api/v1/data-recovery/permission
 */
@GetMapping("/permission")
public ApiResponse<RecoveryPermissionInfo> checkRecoveryPermission(
        HttpServletRequest request) {
    
    Long userId = getCurrentUserId(request);
    
    RecoveryPermissionInfo permissionInfo = dataRecoveryService.getRecoveryPermission(userId);
    return ApiResponse.success(permissionInfo);
}

/**
 * 数据恢复权限信息
 */
@Data
public class RecoveryPermissionInfo {
    private boolean hasPermission;
    private String userType; // free, vip
    private Integer recoveryPeriodMonths; // null for free users
    private LocalDateTime earliestRecoveryDate;
    private RecoveryQuota quota;
    
    @Data
    public static class RecoveryQuota {
        private int deletedTemplates;
        private int deletedImages;
        private int deletedFiles;
        private int recoverableTemplates;
        private int recoverableImages;
        private int recoverableFiles;
    }
}
```

## 4. 数据统计接口

### 4.1 恢复统计接口

```java
/**
 * 获取数据恢复统计信息
 * GET /api/v1/data-recovery/statistics
 */
@GetMapping("/statistics")
public ApiResponse<RecoveryStatistics> getRecoveryStatistics(
        @RequestParam(required = false) String period, // week, month, year
        HttpServletRequest request) {
    
    Long userId = getCurrentUserId(request);
    
    RecoveryStatistics statistics = dataRecoveryService.getRecoveryStatistics(userId, period);
    return ApiResponse.success(statistics);
}

/**
 * 恢复统计信息
 */
@Data
public class RecoveryStatistics {
    private DeletedDataSummary deletedSummary;
    private RecoveryOperationSummary recoverySummary;
    private List<DailyRecoveryCount> dailyCounts;
    
    @Data
    public static class DeletedDataSummary {
        private int totalDeleted;
        private int recoverableCount;
        private int expiredCount;
        private Map<String, Integer> countByType;
    }
    
    @Data
    public static class RecoveryOperationSummary {
        private int totalRecovered;
        private int successfulRecoveries;
        private int failedRecoveries;
        private LocalDateTime lastRecoveryTime;
    }
    
    @Data
    public static class DailyRecoveryCount {
        private LocalDate date;
        private int deletedCount;
        private int recoveredCount;
    }
}

## 5. 管理员接口

### 5.1 数据清理管理接口

```java
/**
 * 管理员数据恢复控制器
 */
@RestController
@RequestMapping("/api/v1/admin/data-recovery")
@PreAuthorize("hasRole('ADMIN')")
public class AdminDataRecoveryController {

    @Autowired
    private AdminDataRecoveryService adminService;

    /**
     * 手动触发数据清理任务
     * POST /api/v1/admin/data-recovery/cleanup
     */
    @PostMapping("/cleanup")
    public ApiResponse<CleanupTaskResponse> triggerCleanup(
            @RequestBody @Valid CleanupRequest request) {

        CleanupTaskResponse response = adminService.triggerDataCleanup(request);
        return ApiResponse.success(response);
    }

    /**
     * 查询清理任务状态
     * GET /api/v1/admin/data-recovery/cleanup/tasks
     */
    @GetMapping("/cleanup/tasks")
    public ApiResponse<PageResponse<CleanupTask>> getCleanupTasks(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {

        PageResponse<CleanupTask> tasks = adminService.getCleanupTasks(page, size, status);
        return ApiResponse.success(tasks);
    }
}

/**
 * 清理请求参数
 */
@Data
@Valid
public class CleanupRequest {
    @NotNull
    private String dataType; // template, image, file, all

    @NotNull
    private String cleanupType; // archive, delete

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate targetDate;

    private boolean dryRun = false; // 是否为试运行
}

/**
 * 清理任务响应
 */
@Data
public class CleanupTaskResponse {
    private Long taskId;
    private String status;
    private String message;
    private LocalDateTime createdAt;
}

/**
 * 清理任务信息
 */
@Data
public class CleanupTask {
    private Long id;
    private String taskType;
    private String dataType;
    private LocalDate targetDate;
    private int processedCount;
    private int totalCount;
    private String status;
    private String errorMessage;
    private LocalDateTime createdAt;
    private LocalDateTime startedAt;
    private LocalDateTime completedAt;
}
```

### 5.2 系统监控接口

```java
/**
 * 获取系统数据恢复监控信息
 * GET /api/v1/admin/data-recovery/monitor
 */
@GetMapping("/monitor")
public ApiResponse<SystemRecoveryMonitor> getSystemMonitor() {

    SystemRecoveryMonitor monitor = adminService.getSystemRecoveryMonitor();
    return ApiResponse.success(monitor);
}

/**
 * 系统恢复监控信息
 */
@Data
public class SystemRecoveryMonitor {
    private StorageUsage storageUsage;
    private PerformanceMetrics performance;
    private ErrorStatistics errors;
    private List<AlertInfo> alerts;

    @Data
    public static class StorageUsage {
        private long totalDeletedData; // 字节
        private long recoverableData; // 字节
        private long expiredData; // 字节
        private double storageGrowthRate; // 增长率
    }

    @Data
    public static class PerformanceMetrics {
        private double avgQueryTime; // 毫秒
        private double avgRestoreTime; // 毫秒
        private int dailyRecoveryCount;
        private int peakConcurrentUsers;
    }

    @Data
    public static class ErrorStatistics {
        private int totalErrors;
        private int recoveryFailures;
        private int permissionDenials;
        private Map<String, Integer> errorsByType;
    }

    @Data
    public static class AlertInfo {
        private String type;
        private String message;
        private String severity; // low, medium, high, critical
        private LocalDateTime createdAt;
    }
}
```

## 6. 接口安全与限流

### 6.1 认证拦截器

```java
/**
 * 数据恢复接口认证拦截器
 */
@Component
public class DataRecoveryAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private UserService userService;

    @Autowired
    private UserPermissionService permissionService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                           Object handler) throws Exception {

        // 1. 验证用户登录状态
        String token = request.getHeader("Authorization");
        if (StringUtils.isEmpty(token)) {
            writeErrorResponse(response, ErrorCodes.UNAUTHORIZED, "未登录");
            return false;
        }

        Long userId = userService.getUserIdFromToken(token);
        if (userId == null) {
            writeErrorResponse(response, ErrorCodes.UNAUTHORIZED, "登录已过期");
            return false;
        }

        // 2. 检查数据恢复权限（仅对恢复相关接口）
        if (isRecoveryOperation(request)) {
            if (!permissionService.hasDataRecoveryPermission(userId)) {
                writeErrorResponse(response, ErrorCodes.NO_RECOVERY_PERMISSION,
                    "无数据恢复权限，请升级VIP");
                return false;
            }
        }

        // 3. 设置用户上下文
        request.setAttribute("userId", userId);
        return true;
    }

    private boolean isRecoveryOperation(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri.contains("/restore") || uri.contains("/batch-restore");
    }

    private void writeErrorResponse(HttpServletResponse response, String errorCode,
                                  String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpStatus.UNAUTHORIZED.value());

        ApiResponse<Object> errorResponse = ApiResponse.error(errorCode, message);
        response.getWriter().write(JSON.toJSONString(errorResponse));
    }
}
```

### 6.2 限流配置

```java
/**
 * 数据恢复接口限流配置
 */
@Configuration
public class DataRecoveryRateLimitConfig {

    /**
     * 查询接口限流：每用户每分钟最多60次
     */
    @Bean
    public RateLimiter queryRateLimiter() {
        return RateLimiter.create(1.0); // 每秒1次
    }

    /**
     * 恢复接口限流：每用户每分钟最多10次
     */
    @Bean
    public RateLimiter restoreRateLimiter() {
        return RateLimiter.create(0.17); // 每6秒1次
    }

    /**
     * 批量恢复接口限流：每用户每小时最多5次
     */
    @Bean
    public RateLimiter batchRestoreRateLimiter() {
        return RateLimiter.create(0.0014); // 每12分钟1次
    }
}
```

## 7. 接口文档示例

### 7.1 Swagger配置

```java
@Configuration
@EnableSwagger2
public class DataRecoverySwaggerConfig {

    @Bean
    public Docket dataRecoveryApi() {
        return new Docket(DocumentationType.SWAGGER_2)
            .groupName("data-recovery")
            .apiInfo(apiInfo())
            .select()
            .apis(RequestHandlerSelectors.basePackage("com.xprinter.recovery.controller"))
            .paths(PathSelectors.ant("/api/v1/data-recovery/**"))
            .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
            .title("XPrinter数据恢复API")
            .description("提供数据恢复相关的接口服务")
            .version("1.0")
            .build();
    }
}
```

### 7.2 接口使用示例

```bash
# 1. 查询已删除数据
curl -X GET "http://api.xprinter.com/api/v1/data-recovery/deleted?dataType=template&page=1&size=20" \
  -H "Authorization: Bearer your_token_here"

# 2. 恢复单个模板
curl -X POST "http://api.xprinter.com/api/v1/data-recovery/restore" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "itemId": 12345,
    "restoreToOriginal": true
  }'

# 3. 批量恢复
curl -X POST "http://api.xprinter.com/api/v1/data-recovery/batch-restore" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "items": [
      {"dataType": "template", "itemId": 12345},
      {"dataType": "image", "itemId": 67890}
    ],
    "restoreToOriginal": true
  }'
```
```
