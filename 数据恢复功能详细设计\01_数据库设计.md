# 数据恢复功能 - 数据库设计

## 1. 核心设计原则

### 1.1 软删除机制
- 所有可恢复的数据采用软删除，不进行物理删除
- 使用 `deleted_at` 字段标记删除状态
- 保留原始创建时间，确保恢复后排序正确

### 1.2 权限分层
- 免费用户：无数据恢复功能
- VIP用户：6个月内数据恢复

## 2. 数据库表结构设计

### 2.1 用户模板表 (user_templates)

```sql
CREATE TABLE `user_templates` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `template_name` varchar(255) NOT NULL COMMENT '模板名称',
  `template_data` longtext NOT NULL COMMENT '模板数据JSON',
  `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `category` varchar(50) DEFAULT 'personal' COMMENT '分类：personal/shared/recovered_templates',
  `original_category` varchar(50) DEFAULT NULL COMMENT '原始分类（恢复时使用）',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签，逗号分隔',
  `is_favorite` tinyint(1) DEFAULT 0 COMMENT '是否收藏',
  `is_recovered` tinyint(1) DEFAULT 0 COMMENT '是否为恢复的数据',
  `recovered_at` timestamp NULL DEFAULT NULL COMMENT '恢复时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间，NULL表示未删除',
  `deleted_by` bigint(20) DEFAULT NULL COMMENT '删除操作用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_deleted` (`user_id`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户模板表';
```

### 2.2 云端文件表 (cloud_files)

```sql
CREATE TABLE `cloud_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_type` varchar(20) NOT NULL COMMENT '文件类型：image/document',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小(字节)',
  `file_url` varchar(500) NOT NULL COMMENT '文件存储URL',
  `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME类型',
  `upload_source` varchar(50) DEFAULT 'manual' COMMENT '上传来源：manual/scan/ocr',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `deleted_by` bigint(20) DEFAULT NULL COMMENT '删除操作用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_user_deleted` (`user_id`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云端文件表';
```

### 2.3 数据恢复记录表 (data_recovery_logs)

```sql
CREATE TABLE `data_recovery_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '恢复记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `recovery_type` varchar(20) NOT NULL COMMENT '恢复类型：template/image/file',
  `target_id` bigint(20) NOT NULL COMMENT '目标数据ID',
  `target_name` varchar(255) NOT NULL COMMENT '目标数据名称',
  `original_deleted_at` timestamp NOT NULL COMMENT '原始删除时间',
  `recovery_status` varchar(20) NOT NULL DEFAULT 'success' COMMENT '恢复状态：success/failed',
  `recovery_reason` varchar(500) DEFAULT NULL COMMENT '恢复失败原因',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '恢复时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_recovery_type` (`recovery_type`),
  KEY `idx_target_id` (`target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据恢复记录表';
```

### 2.4 用户权限表 (user_permissions)

```sql
CREATE TABLE `user_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `permission_type` varchar(50) NOT NULL COMMENT '权限类型',
  `permission_value` varchar(100) NOT NULL COMMENT '权限值',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_permission` (`user_id`, `permission_type`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户权限表';
```

### 2.5 数据清理任务表 (data_cleanup_tasks)

```sql
CREATE TABLE `data_cleanup_tasks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型：archive/delete',
  `data_type` varchar(20) NOT NULL COMMENT '数据类型：template/image/file',
  `target_date` date NOT NULL COMMENT '目标清理日期',
  `processed_count` int(11) DEFAULT 0 COMMENT '已处理数量',
  `total_count` int(11) DEFAULT 0 COMMENT '总数量',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending/running/completed/failed',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_status` (`status`),
  KEY `idx_target_date` (`target_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据清理任务表';
```

## 3. 索引优化策略

### 3.1 查询优化索引
```sql
-- 用户已删除数据查询优化
CREATE INDEX idx_user_deleted_created ON user_templates(user_id, deleted_at, created_at);
CREATE INDEX idx_user_deleted_created ON cloud_files(user_id, deleted_at, created_at);

-- 数据恢复时间范围查询优化
CREATE INDEX idx_deleted_range ON user_templates(deleted_at, user_id);
CREATE INDEX idx_deleted_range ON cloud_files(deleted_at, user_id);
```

### 3.2 分区策略
```sql
-- 按月分区恢复记录表（可选，数据量大时使用）
ALTER TABLE data_recovery_logs 
PARTITION BY RANGE (YEAR(created_at)*100 + MONTH(created_at)) (
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    -- ... 继续添加分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 4. 数据一致性保证

### 4.1 事务处理
- 软删除操作必须在事务中执行
- 恢复操作需要记录恢复日志
- 批量操作使用批处理事务

### 4.2 数据完整性约束
```sql
-- 添加检查约束
ALTER TABLE user_templates ADD CONSTRAINT chk_deleted_by
CHECK (deleted_at IS NULL OR deleted_by IS NOT NULL);

ALTER TABLE cloud_files ADD CONSTRAINT chk_deleted_by
CHECK (deleted_at IS NULL OR deleted_by IS NOT NULL);
```

## 5. 权限数据初始化

### 5.1 VIP权限配置
```sql
-- VIP用户数据恢复权限
INSERT INTO user_permissions (user_id, permission_type, permission_value, expires_at)
VALUES
(?, 'data_recovery', '6_months', DATE_ADD(NOW(), INTERVAL 1 YEAR)),
(?, 'unlimited_templates', 'true', DATE_ADD(NOW(), INTERVAL 1 YEAR));

-- 免费用户基础权限
INSERT INTO user_permissions (user_id, permission_type, permission_value, expires_at)
VALUES
(?, 'template_limit', '100', NULL),
(?, 'data_recovery', 'false', NULL);
```

### 5.2 权限检查函数
```sql
DELIMITER //
CREATE FUNCTION check_data_recovery_permission(p_user_id BIGINT)
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_permission VARCHAR(100);
    DECLARE v_expires_at TIMESTAMP;

    SELECT permission_value, expires_at
    INTO v_permission, v_expires_at
    FROM user_permissions
    WHERE user_id = p_user_id
    AND permission_type = 'data_recovery'
    AND (expires_at IS NULL OR expires_at > NOW());

    RETURN v_permission = '6_months' OR v_permission = 'true';
END //
DELIMITER ;
```

## 6. 数据迁移脚本

### 6.1 现有数据软删除改造
```sql
-- 为现有表添加软删除字段
ALTER TABLE user_templates
ADD COLUMN deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '删除时间',
ADD COLUMN deleted_by BIGINT(20) DEFAULT NULL COMMENT '删除操作用户ID';

ALTER TABLE cloud_files
ADD COLUMN deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '删除时间',
ADD COLUMN deleted_by BIGINT(20) DEFAULT NULL COMMENT '删除操作用户ID';

-- 添加索引
CREATE INDEX idx_deleted_at ON user_templates(deleted_at);
CREATE INDEX idx_deleted_at ON cloud_files(deleted_at);
```

### 6.2 历史数据处理
```sql
-- 标记历史已删除数据（如果有删除标记字段）
UPDATE user_templates
SET deleted_at = updated_at, deleted_by = user_id
WHERE is_deleted = 1;

-- 清理无效的删除标记字段
ALTER TABLE user_templates DROP COLUMN is_deleted;
```

## 7. 性能优化建议

### 7.1 查询优化
- 使用覆盖索引减少回表查询
- 分页查询使用游标分页而非OFFSET
- 定期分析表统计信息

### 7.2 存储优化
- 定期清理6个月以上的已删除数据
- 使用表分区管理大量历史数据
- 考虑使用压缩存储降低空间占用

### 7.3 监控指标
- 已删除数据总量
- 数据恢复成功率
- 查询响应时间
- 存储空间使用率
