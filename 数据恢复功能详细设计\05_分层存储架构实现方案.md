# 数据恢复功能 - 分层存储架构实现方案

## 1. 分层存储架构概述

### 1.1 存储层级设计

```
Level 1: 本地存储 (SQLite/Room)
├── 最近3天数据
├── 快速访问
├── 离线可用
└── 自动同步到云端

Level 2: 云端热存储 (MySQL + Redis)
├── 免费用户: 无数据恢复功能
├── VIP用户: 6个月内数据恢复
├── 实时同步
└── 高性能查询

Level 3: 云端冷存储 (阿里云OSS)
├── 历史数据归档 (6个月以上)
├── 成本优化
├── 按需恢复
└── 压缩存储
```

### 1.2 数据流转策略

```java
/**
 * 数据存储策略枚举
 */
public enum StorageStrategy {
    LOCAL_ONLY("仅本地存储", 0),
    HOT_STORAGE("热存储", 1),
    COLD_STORAGE("冷存储", 2),
    ARCHIVED("已归档", 3);
    
    private final String description;
    private final int level;
    
    StorageStrategy(String description, int level) {
        this.description = description;
        this.level = level;
    }
}

/**
 * 数据生命周期管理
 */
public enum DataLifecycle {
    ACTIVE("活跃数据", 0, 3), // 3天内
    RECENT("近期数据", 3, 30), // 3-30天
    RECOVERABLE("可恢复数据", 30, 180), // 30天-6个月
    ARCHIVED("归档数据", 180, -1); // 6个月以上
    
    private final String description;
    private final int startDays;
    private final int endDays; // -1表示无限制
    
    DataLifecycle(String description, int startDays, int endDays) {
        this.description = description;
        this.startDays = startDays;
        this.endDays = endDays;
    }
}
```

## 2. 本地存储实现

### 2.1 本地数据库设计

```java
/**
 * 本地模板实体
 */
@Entity(tableName = "local_templates")
@Data
public class LocalTemplate {
    @PrimaryKey
    private Long id;
    
    @ColumnInfo(name = "user_id")
    private Long userId;
    
    @ColumnInfo(name = "template_name")
    private String templateName;
    
    @ColumnInfo(name = "template_data")
    private String templateData;
    
    @ColumnInfo(name = "thumbnail_path")
    private String thumbnailPath;
    
    @ColumnInfo(name = "sync_status")
    private String syncStatus; // PENDING, SYNCED, FAILED
    
    @ColumnInfo(name = "created_at")
    private Long createdAt;
    
    @ColumnInfo(name = "updated_at")
    private Long updatedAt;
    
    @ColumnInfo(name = "deleted_at")
    private Long deletedAt;
    
    @ColumnInfo(name = "cloud_id")
    private Long cloudId; // 对应云端ID
}

/**
 * 本地数据访问对象
 */
@Dao
public interface LocalTemplateDao {
    
    @Query("SELECT * FROM local_templates WHERE user_id = :userId AND deleted_at IS NULL ORDER BY created_at DESC")
    List<LocalTemplate> getActiveTemplates(Long userId);
    
    @Query("SELECT * FROM local_templates WHERE user_id = :userId AND deleted_at IS NOT NULL AND deleted_at > :cutoffTime ORDER BY deleted_at DESC")
    List<LocalTemplate> getDeletedTemplates(Long userId, Long cutoffTime);
    
    @Query("UPDATE local_templates SET deleted_at = :deletedAt WHERE id = :id")
    void softDelete(Long id, Long deletedAt);
    
    @Query("UPDATE local_templates SET deleted_at = NULL WHERE id = :id")
    void restore(Long id);
    
    @Query("DELETE FROM local_templates WHERE deleted_at IS NOT NULL AND deleted_at < :cutoffTime")
    void cleanupOldDeletedData(Long cutoffTime);
}
```

### 2.2 本地存储服务

```java
/**
 * 本地存储服务
 */
@Service
public class LocalStorageService {
    
    @Autowired
    private LocalTemplateDao templateDao;
    
    @Autowired
    private CloudSyncService cloudSyncService;
    
    private static final long THREE_DAYS_MILLIS = 3 * 24 * 60 * 60 * 1000L;
    
    /**
     * 保存模板到本地
     */
    public void saveTemplate(LocalTemplate template) {
        template.setCreatedAt(System.currentTimeMillis());
        template.setUpdatedAt(System.currentTimeMillis());
        template.setSyncStatus("PENDING");
        
        templateDao.insert(template);
        
        // 异步同步到云端
        cloudSyncService.syncTemplateAsync(template);
    }
    
    /**
     * 软删除本地模板
     */
    public void softDeleteTemplate(Long templateId) {
        long now = System.currentTimeMillis();
        templateDao.softDelete(templateId, now);
        
        // 同步删除状态到云端
        cloudSyncService.syncDeleteStatusAsync(templateId, now);
    }
    
    /**
     * 恢复本地模板
     */
    public void restoreTemplate(Long templateId) {
        templateDao.restore(templateId);
        
        // 同步恢复状态到云端
        cloudSyncService.syncRestoreStatusAsync(templateId);
    }
    
    /**
     * 获取本地已删除数据
     */
    public List<LocalTemplate> getLocalDeletedTemplates(Long userId) {
        long cutoffTime = System.currentTimeMillis() - THREE_DAYS_MILLIS;
        return templateDao.getDeletedTemplates(userId, cutoffTime);
    }
    
    /**
     * 清理过期的本地删除数据
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupExpiredLocalData() {
        long cutoffTime = System.currentTimeMillis() - THREE_DAYS_MILLIS;
        templateDao.cleanupOldDeletedData(cutoffTime);
        
        log.info("清理过期本地删除数据完成, cutoffTime: {}", new Date(cutoffTime));
    }
}
```

## 3. 云端热存储实现

### 3.1 热存储数据管理

```java
/**
 * 热存储数据管理服务
 */
@Service
public class HotStorageService {
    
    @Autowired
    private UserTemplateMapper templateMapper;
    
    @Autowired
    private CloudFileMapper fileMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private ColdStorageService coldStorageService;
    
    private static final String DELETED_DATA_CACHE_KEY = "deleted_data:";
    private static final int CACHE_EXPIRE_HOURS = 24;
    
    /**
     * 查询热存储中的已删除数据
     */
    public PageResult<DeletedDataItem> queryHotStorageDeletedData(DeletedDataQueryParam param) {
        // 先从缓存查询
        String cacheKey = buildCacheKey(param);
        PageResult<DeletedDataItem> cached = (PageResult<DeletedDataItem>) 
            redisTemplate.opsForValue().get(cacheKey);
        
        if (cached != null) {
            return cached;
        }
        
        // 从数据库查询
        PageResult<DeletedDataItem> result = queryFromDatabase(param);
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, result, 
            Duration.ofHours(CACHE_EXPIRE_HOURS));
        
        return result;
    }
    
    /**
     * 从数据库查询已删除数据
     */
    private PageResult<DeletedDataItem> queryFromDatabase(DeletedDataQueryParam param) {
        List<DeletedDataItem> items = new ArrayList<>();
        long total = 0;
        
        // 查询模板数据
        if ("template".equals(param.getDataType()) || "all".equals(param.getDataType())) {
            List<UserTemplate> templates = templateMapper.selectDeletedByUserId(
                param.getUserId(), param.getStartDate(), param.getEndDate());
            
            items.addAll(templates.stream()
                .map(this::convertToDeletedDataItem)
                .collect(Collectors.toList()));
            
            total += templateMapper.countDeletedByUserId(
                param.getUserId(), param.getStartDate(), param.getEndDate());
        }
        
        // 查询文件数据
        if ("file".equals(param.getDataType()) || "image".equals(param.getDataType()) || 
            "all".equals(param.getDataType())) {
            
            String fileType = "all".equals(param.getDataType()) ? null : param.getDataType();
            List<CloudFile> files = fileMapper.selectDeletedByUserId(
                param.getUserId(), fileType, param.getStartDate(), param.getEndDate());
            
            items.addAll(files.stream()
                .map(this::convertToDeletedDataItem)
                .collect(Collectors.toList()));
            
            total += fileMapper.countDeletedByUserId(
                param.getUserId(), fileType, param.getStartDate(), param.getEndDate());
        }
        
        // 排序和分页
        items.sort((a, b) -> b.getDeletedTime().compareTo(a.getDeletedTime()));
        
        int start = (param.getPage() - 1) * param.getSize();
        int end = Math.min(start + param.getSize(), items.size());
        List<DeletedDataItem> pageItems = items.subList(start, end);
        
        return PageResult.<DeletedDataItem>builder()
            .items(pageItems)
            .total(total)
            .page(param.getPage())
            .size(param.getSize())
            .hasNext(end < items.size())
            .build();
    }
    
    /**
     * 数据迁移到冷存储
     */
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void migrateToColdstorage() {
        LocalDateTime cutoffDate = LocalDateTime.now().minusMonths(6);
        
        // 迁移模板数据
        List<UserTemplate> expiredTemplates = templateMapper.selectExpiredDeleted(cutoffDate);
        for (UserTemplate template : expiredTemplates) {
            try {
                coldStorageService.archiveTemplate(template);
                templateMapper.markAsArchived(template.getId());
                log.info("模板迁移到冷存储成功, templateId: {}", template.getId());
            } catch (Exception e) {
                log.error("模板迁移到冷存储失败, templateId: {}", template.getId(), e);
            }
        }
        
        // 迁移文件数据
        List<CloudFile> expiredFiles = fileMapper.selectExpiredDeleted(cutoffDate);
        for (CloudFile file : expiredFiles) {
            try {
                coldStorageService.archiveFile(file);
                fileMapper.markAsArchived(file.getId());
                log.info("文件迁移到冷存储成功, fileId: {}", file.getId());
            } catch (Exception e) {
                log.error("文件迁移到冷存储失败, fileId: {}", file.getId(), e);
            }
        }
    }
    
    private String buildCacheKey(DeletedDataQueryParam param) {
        return DELETED_DATA_CACHE_KEY + param.getUserId() + ":" + 
               param.getDataType() + ":" + param.getPage() + ":" + param.getSize();
    }
    
    private DeletedDataItem convertToDeletedDataItem(UserTemplate template) {
        DeletedDataItem item = new DeletedDataItem();
        item.setId(template.getId());
        item.setName(template.getTemplateName());
        item.setType("template");
        item.setCreatedTime(template.getCreatedAt());
        item.setDeletedTime(template.getDeletedAt());
        item.setThumbnailUrl(template.getThumbnailUrl());
        item.setCanRecover(isWithinRecoveryPeriod(template.getDeletedAt()));
        item.setOriginalPosition("personal_templates");
        return item;
    }
    
    private DeletedDataItem convertToDeletedDataItem(CloudFile file) {
        DeletedDataItem item = new DeletedDataItem();
        item.setId(file.getId());
        item.setName(file.getFileName());
        item.setType(file.getFileType());
        item.setCreatedTime(file.getCreatedAt());
        item.setDeletedTime(file.getDeletedAt());
        item.setThumbnailUrl(file.getThumbnailUrl());
        item.setCanRecover(isWithinRecoveryPeriod(file.getDeletedAt()));
        item.setOriginalPosition("cloud_files");
        return item;
    }
    
    private boolean isWithinRecoveryPeriod(LocalDateTime deletedAt) {
        return deletedAt != null &&
               deletedAt.isAfter(LocalDateTime.now().minusMonths(6));
    }
}

## 4. 云端冷存储实现

### 4.1 冷存储服务

```java
/**
 * 冷存储服务（基于阿里云OSS）
 */
@Service
public class ColdStorageService {

    @Autowired
    private OSSClient ossClient;

    @Autowired
    private ArchiveRecordMapper archiveRecordMapper;

    private static final String ARCHIVE_BUCKET = "xprinter-archive";
    private static final String TEMPLATE_PREFIX = "templates/";
    private static final String FILE_PREFIX = "files/";

    /**
     * 归档模板到冷存储
     */
    public void archiveTemplate(UserTemplate template) {
        try {
            // 1. 构建归档数据
            ArchiveData archiveData = buildTemplateArchiveData(template);
            String archiveJson = JSON.toJSONString(archiveData);

            // 2. 压缩数据
            byte[] compressedData = compressData(archiveJson.getBytes(StandardCharsets.UTF_8));

            // 3. 上传到OSS冷存储
            String objectKey = TEMPLATE_PREFIX + template.getUserId() + "/" +
                              template.getId() + "_" + System.currentTimeMillis() + ".gz";

            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(compressedData.length);
            metadata.setContentType("application/gzip");
            metadata.setStorageClass(StorageClass.ColdArchive); // 冷归档存储
            metadata.addUserMetadata("original_id", template.getId().toString());
            metadata.addUserMetadata("user_id", template.getUserId().toString());
            metadata.addUserMetadata("data_type", "template");
            metadata.addUserMetadata("deleted_at", template.getDeletedAt().toString());

            PutObjectRequest request = new PutObjectRequest(ARCHIVE_BUCKET, objectKey,
                new ByteArrayInputStream(compressedData), metadata);

            PutObjectResult result = ossClient.putObject(request);

            // 4. 记录归档信息
            ArchiveRecord record = new ArchiveRecord();
            record.setOriginalId(template.getId());
            record.setUserId(template.getUserId());
            record.setDataType("template");
            record.setArchiveKey(objectKey);
            record.setOriginalSize((long) archiveJson.length());
            record.setCompressedSize((long) compressedData.length);
            record.setEtag(result.getETag());
            record.setArchivedAt(LocalDateTime.now());
            record.setCreatedAt(LocalDateTime.now());

            archiveRecordMapper.insert(record);

            log.info("模板归档成功, templateId: {}, archiveKey: {}", template.getId(), objectKey);

        } catch (Exception e) {
            log.error("模板归档失败, templateId: {}", template.getId(), e);
            throw new ColdStorageException("模板归档失败", e);
        }
    }

    /**
     * 归档文件到冷存储
     */
    public void archiveFile(CloudFile file) {
        try {
            // 1. 构建归档数据
            ArchiveData archiveData = buildFileArchiveData(file);
            String archiveJson = JSON.toJSONString(archiveData);

            // 2. 如果原文件还存在，一起归档
            byte[] originalFileData = null;
            if (StringUtils.isNotEmpty(file.getFileUrl())) {
                try {
                    originalFileData = downloadOriginalFile(file.getFileUrl());
                } catch (Exception e) {
                    log.warn("下载原文件失败, fileId: {}, url: {}", file.getId(), file.getFileUrl(), e);
                }
            }

            // 3. 创建归档包
            byte[] archivePackage = createArchivePackage(archiveJson, originalFileData);
            byte[] compressedPackage = compressData(archivePackage);

            // 4. 上传到OSS冷存储
            String objectKey = FILE_PREFIX + file.getUserId() + "/" +
                              file.getId() + "_" + System.currentTimeMillis() + ".tar.gz";

            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(compressedPackage.length);
            metadata.setContentType("application/gzip");
            metadata.setStorageClass(StorageClass.ColdArchive);
            metadata.addUserMetadata("original_id", file.getId().toString());
            metadata.addUserMetadata("user_id", file.getUserId().toString());
            metadata.addUserMetadata("data_type", "file");
            metadata.addUserMetadata("file_type", file.getFileType());
            metadata.addUserMetadata("deleted_at", file.getDeletedAt().toString());

            PutObjectRequest request = new PutObjectRequest(ARCHIVE_BUCKET, objectKey,
                new ByteArrayInputStream(compressedPackage), metadata);

            PutObjectResult result = ossClient.putObject(request);

            // 5. 记录归档信息
            ArchiveRecord record = new ArchiveRecord();
            record.setOriginalId(file.getId());
            record.setUserId(file.getUserId());
            record.setDataType("file");
            record.setArchiveKey(objectKey);
            record.setOriginalSize(file.getFileSize());
            record.setCompressedSize((long) compressedPackage.length);
            record.setEtag(result.getETag());
            record.setArchivedAt(LocalDateTime.now());
            record.setCreatedAt(LocalDateTime.now());

            archiveRecordMapper.insert(record);

            log.info("文件归档成功, fileId: {}, archiveKey: {}", file.getId(), objectKey);

        } catch (Exception e) {
            log.error("文件归档失败, fileId: {}", file.getId(), e);
            throw new ColdStorageException("文件归档失败", e);
        }
    }

    /**
     * 从冷存储恢复数据
     */
    public RestoreFromColdStorageResult restoreFromColdStorage(Long originalId, String dataType) {
        try {
            // 1. 查询归档记录
            ArchiveRecord record = archiveRecordMapper.selectByOriginalIdAndType(originalId, dataType);
            if (record == null) {
                return RestoreFromColdStorageResult.failure("归档记录不存在");
            }

            // 2. 从OSS下载归档数据
            GetObjectRequest request = new GetObjectRequest(ARCHIVE_BUCKET, record.getArchiveKey());
            OSSObject ossObject = ossClient.getObject(request);

            byte[] compressedData = IOUtils.toByteArray(ossObject.getObjectContent());
            byte[] decompressedData = decompressData(compressedData);

            // 3. 解析归档数据
            if ("template".equals(dataType)) {
                return restoreTemplateFromArchive(decompressedData, record);
            } else if ("file".equals(dataType)) {
                return restoreFileFromArchive(decompressedData, record);
            } else {
                return RestoreFromColdStorageResult.failure("不支持的数据类型");
            }

        } catch (Exception e) {
            log.error("从冷存储恢复数据失败, originalId: {}, dataType: {}", originalId, dataType, e);
            return RestoreFromColdStorageResult.failure("恢复失败: " + e.getMessage());
        }
    }

    /**
     * 压缩数据
     */
    private byte[] compressData(byte[] data) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
            gzipOut.write(data);
        }
        return baos.toByteArray();
    }

    /**
     * 解压数据
     */
    private byte[] decompressData(byte[] compressedData) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPInputStream gzipIn = new GZIPInputStream(new ByteArrayInputStream(compressedData))) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipIn.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
        }
        return baos.toByteArray();
    }

    /**
     * 构建模板归档数据
     */
    private ArchiveData buildTemplateArchiveData(UserTemplate template) {
        ArchiveData archiveData = new ArchiveData();
        archiveData.setId(template.getId());
        archiveData.setUserId(template.getUserId());
        archiveData.setDataType("template");
        archiveData.setName(template.getTemplateName());
        archiveData.setData(template.getTemplateData());
        archiveData.setThumbnailUrl(template.getThumbnailUrl());
        archiveData.setCreatedAt(template.getCreatedAt());
        archiveData.setDeletedAt(template.getDeletedAt());
        archiveData.setMetadata(buildTemplateMetadata(template));
        return archiveData;
    }

    /**
     * 构建文件归档数据
     */
    private ArchiveData buildFileArchiveData(CloudFile file) {
        ArchiveData archiveData = new ArchiveData();
        archiveData.setId(file.getId());
        archiveData.setUserId(file.getUserId());
        archiveData.setDataType("file");
        archiveData.setName(file.getFileName());
        archiveData.setFileUrl(file.getFileUrl());
        archiveData.setThumbnailUrl(file.getThumbnailUrl());
        archiveData.setFileSize(file.getFileSize());
        archiveData.setMimeType(file.getMimeType());
        archiveData.setCreatedAt(file.getCreatedAt());
        archiveData.setDeletedAt(file.getDeletedAt());
        archiveData.setMetadata(buildFileMetadata(file));
        return archiveData;
    }
}

/**
 * 归档数据结构
 */
@Data
public class ArchiveData {
    private Long id;
    private Long userId;
    private String dataType;
    private String name;
    private String data; // 模板数据或文件内容
    private String fileUrl;
    private String thumbnailUrl;
    private Long fileSize;
    private String mimeType;
    private LocalDateTime createdAt;
    private LocalDateTime deletedAt;
    private Map<String, Object> metadata;
}

/**
 * 归档记录实体
 */
@Data
@Entity
@Table(name = "archive_records")
public class ArchiveRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "original_id", nullable = false)
    private Long originalId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "data_type", nullable = false)
    private String dataType;

    @Column(name = "archive_key", nullable = false)
    private String archiveKey;

    @Column(name = "original_size")
    private Long originalSize;

    @Column(name = "compressed_size")
    private Long compressedSize;

    @Column(name = "etag")
    private String etag;

    @Column(name = "archived_at", nullable = false)
    private LocalDateTime archivedAt;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
}
```
