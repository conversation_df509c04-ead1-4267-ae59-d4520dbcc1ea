# XPrinter VIP体系API接口设计

## 1. API设计原则

### 1.1 设计目标
- **统一规范**：遵循RESTful设计规范，保持接口一致性
- **权限安全**：所有VIP相关接口都需要严格的权限验证
- **响应快速**：接口响应时间控制在100ms以内
- **易于集成**：提供清晰的文档和示例代码

### 1.2 通用响应格式

```java
/**
 * 统一API响应格式
 */
@Data
public class ApiResponse<T> {
    private int code;
    private String message;
    private T data;
    private long timestamp;
    private String requestId;
    
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(200);
        response.setMessage("success");
        response.setData(data);
        response.setTimestamp(System.currentTimeMillis());
        response.setRequestId(generateRequestId());
        return response;
    }
    
    public static <T> ApiResponse<T> error(int code, String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(code);
        response.setMessage(message);
        response.setTimestamp(System.currentTimeMillis());
        response.setRequestId(generateRequestId());
        return response;
    }
}
```

## 2. VIP订阅管理接口

### 2.1 VIP套餐查询接口

```java
/**
 * VIP套餐管理控制器
 */
@RestController
@RequestMapping("/api/v1/vip/plans")
@Validated
public class VipPlanController {
    
    @Autowired
    private VipPlanService planService;
    
    /**
     * 获取所有可用的VIP套餐
     * GET /api/v1/vip/plans
     */
    @GetMapping
    public ApiResponse<List<VipPlanResponse>> getAvailablePlans() {
        List<VipPlan> plans = planService.getActivePlans();
        List<VipPlanResponse> responses = plans.stream()
            .map(this::convertToResponse)
            .collect(Collectors.toList());
        
        return ApiResponse.success(responses);
    }
    
    /**
     * 获取推荐套餐
     * GET /api/v1/vip/plans/recommended
     */
    @GetMapping("/recommended")
    public ApiResponse<VipPlanResponse> getRecommendedPlan(
            @RequestParam(required = false) String userType,
            HttpServletRequest request) {
        
        Long userId = getCurrentUserId(request);
        VipPlan recommendedPlan = planService.getRecommendedPlan(userId, userType);
        
        return ApiResponse.success(convertToResponse(recommendedPlan));
    }
    
    /**
     * 获取套餐详情
     * GET /api/v1/vip/plans/{planId}
     */
    @GetMapping("/{planId}")
    public ApiResponse<VipPlanDetailResponse> getPlanDetail(@PathVariable String planId) {
        VipPlan plan = planService.getPlanById(planId);
        if (plan == null) {
            return ApiResponse.error(404, "套餐不存在");
        }
        
        VipPlanDetailResponse response = convertToDetailResponse(plan);
        return ApiResponse.success(response);
    }
    
    private VipPlanResponse convertToResponse(VipPlan plan) {
        return VipPlanResponse.builder()
            .planId(plan.getPlanId())
            .planName(plan.getPlanName())
            .userTier(plan.getUserTier().name())
            .durationType(plan.getDurationType())
            .durationValue(plan.getDurationValue())
            .price(plan.getPrice())
            .originalPrice(plan.getOriginalPrice())
            .currency(plan.getCurrency())
            .description(plan.getDescription())
            .features(parseFeatures(plan.getFeaturesJson()))
            .isRecommended(plan.getSortOrder() == 2) // 年度版推荐
            .discountPercentage(calculateDiscountPercentage(plan))
            .build();
    }
}

/**
 * VIP套餐响应
 */
@Data
@Builder
public class VipPlanResponse {
    private String planId;
    private String planName;
    private String userTier;
    private String durationType;
    private Integer durationValue;
    private BigDecimal price;
    private BigDecimal originalPrice;
    private String currency;
    private String description;
    private List<String> features;
    private boolean isRecommended;
    private Integer discountPercentage;
}
```

### 2.2 VIP订阅管理接口

```java
/**
 * VIP订阅管理控制器
 */
@RestController
@RequestMapping("/api/v1/vip/subscriptions")
@Validated
public class VipSubscriptionController {
    
    @Autowired
    private VipSubscriptionService subscriptionService;
    
    @Autowired
    private VipOrderService orderService;
    
    /**
     * 创建VIP订阅
     * POST /api/v1/vip/subscriptions
     */
    @PostMapping
    public ApiResponse<CreateSubscriptionResponse> createSubscription(
            @RequestBody @Valid CreateSubscriptionRequest request,
            HttpServletRequest httpRequest) {
        
        Long userId = getCurrentUserId(httpRequest);
        request.setUserId(userId);
        
        SubscriptionResult result = subscriptionService.createSubscription(request);
        
        if (result.isSuccess()) {
            CreateSubscriptionResponse response = CreateSubscriptionResponse.builder()
                .subscriptionId(result.getSubscription().getId())
                .orderId(result.getOrder().getOrderId())
                .planId(result.getSubscription().getPlanId())
                .userTier(result.getSubscription().getUserTier().name())
                .amount(result.getOrder().getAmount())
                .currency(result.getOrder().getCurrency())
                .paymentUrl(generatePaymentUrl(result.getOrder()))
                .expiresAt(result.getSubscription().getExpiresAt())
                .build();
            
            return ApiResponse.success(response);
        } else {
            return ApiResponse.error(400, result.getErrorMessage());
        }
    }
    
    /**
     * 获取当前用户的VIP订阅状态
     * GET /api/v1/vip/subscriptions/current
     */
    @GetMapping("/current")
    public ApiResponse<CurrentSubscriptionResponse> getCurrentSubscription(
            HttpServletRequest request) {
        
        Long userId = getCurrentUserId(request);
        UserVipSubscription subscription = subscriptionService.getActiveSubscription(userId);
        
        if (subscription == null) {
            return ApiResponse.success(CurrentSubscriptionResponse.free());
        }
        
        CurrentSubscriptionResponse response = CurrentSubscriptionResponse.builder()
            .hasActiveSubscription(true)
            .subscriptionId(subscription.getId())
            .planId(subscription.getPlanId())
            .userTier(subscription.getUserTier().name())
            .status(subscription.getStatus().name())
            .startedAt(subscription.getStartedAt())
            .expiresAt(subscription.getExpiresAt())
            .autoRenew(subscription.getAutoRenew())
            .daysUntilExpiry(calculateDaysUntilExpiry(subscription.getExpiresAt()))
            .isExpiringSoon(isExpiringSoon(subscription.getExpiresAt()))
            .build();
        
        return ApiResponse.success(response);
    }
    
    /**
     * 续费VIP订阅
     * POST /api/v1/vip/subscriptions/renew
     */
    @PostMapping("/renew")
    public ApiResponse<RenewSubscriptionResponse> renewSubscription(
            @RequestBody @Valid RenewSubscriptionRequest request,
            HttpServletRequest httpRequest) {
        
        Long userId = getCurrentUserId(httpRequest);
        
        SubscriptionResult result = subscriptionService.renewSubscription(
            userId, request.getPlanId(), request.isAutoRenew());
        
        if (result.isSuccess()) {
            RenewSubscriptionResponse response = RenewSubscriptionResponse.builder()
                .orderId(result.getOrder().getOrderId())
                .amount(result.getOrder().getAmount())
                .currency(result.getOrder().getCurrency())
                .paymentUrl(generatePaymentUrl(result.getOrder()))
                .newExpiryDate(result.getSubscription().getExpiresAt())
                .build();
            
            return ApiResponse.success(response);
        } else {
            return ApiResponse.error(400, result.getErrorMessage());
        }
    }
    
    /**
     * 取消VIP订阅
     * POST /api/v1/vip/subscriptions/cancel
     */
    @PostMapping("/cancel")
    public ApiResponse<Void> cancelSubscription(
            @RequestBody @Valid CancelSubscriptionRequest request,
            HttpServletRequest httpRequest) {
        
        Long userId = getCurrentUserId(httpRequest);
        
        try {
            subscriptionService.cancelSubscription(userId, request.getCancelReason());
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("取消订阅失败, userId: {}", userId, e);
            return ApiResponse.error(500, "取消订阅失败");
        }
    }
    
    /**
     * 设置自动续费
     * PUT /api/v1/vip/subscriptions/auto-renew
     */
    @PutMapping("/auto-renew")
    public ApiResponse<Void> setAutoRenew(
            @RequestBody @Valid SetAutoRenewRequest request,
            HttpServletRequest httpRequest) {
        
        Long userId = getCurrentUserId(httpRequest);
        
        try {
            subscriptionService.setAutoRenew(userId, request.isAutoRenew());
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("设置自动续费失败, userId: {}", userId, e);
            return ApiResponse.error(500, "设置失败");
        }
    }
}

/**
 * 创建订阅请求
 */
@Data
@Valid
public class CreateSubscriptionRequest {
    private Long userId; // 由后端设置
    
    @NotBlank
    private String planId;
    
    private boolean autoRenew = false;
    
    private String paymentMethod = "alipay";
    
    private String couponCode;
}

/**
 * 当前订阅响应
 */
@Data
@Builder
public class CurrentSubscriptionResponse {
    private boolean hasActiveSubscription;
    private Long subscriptionId;
    private String planId;
    private String userTier;
    private String status;
    private LocalDateTime startedAt;
    private LocalDateTime expiresAt;
    private Boolean autoRenew;
    private Long daysUntilExpiry;
    private boolean isExpiringSoon;
    
    public static CurrentSubscriptionResponse free() {
        return CurrentSubscriptionResponse.builder()
            .hasActiveSubscription(false)
            .userTier("FREE")
            .build();
    }
}

## 3. VIP权限查询接口

### 3.1 权限检查接口

```java
/**
 * VIP权限查询控制器
 */
@RestController
@RequestMapping("/api/v1/vip/permissions")
@Validated
public class VipPermissionController {

    @Autowired
    private VipPermissionService permissionService;

    @Autowired
    private DataRecoveryFeatureController dataRecoveryController;

    @Autowired
    private OcrFeatureController ocrController;

    @Autowired
    private StorageQuotaController storageController;

    /**
     * 获取用户所有权限信息
     * GET /api/v1/vip/permissions
     */
    @GetMapping
    public ApiResponse<UserPermissionsResponse> getUserPermissions(
            HttpServletRequest request) {

        Long userId = getCurrentUserId(request);

        UserPermissionsResponse response = UserPermissionsResponse.builder()
            .userId(userId)
            .dataRecovery(getDataRecoveryPermission(userId))
            .ocrCreate(getOcrPermission(userId))
            .storage(getStoragePermission(userId))
            .teamCollaboration(getTeamCollaborationPermission(userId))
            .experience(getExperiencePermission(userId))
            .build();

        return ApiResponse.success(response);
    }

    /**
     * 检查特定功能权限
     * GET /api/v1/vip/permissions/check
     */
    @GetMapping("/check")
    public ApiResponse<PermissionCheckResponse> checkPermission(
            @RequestParam String feature,
            @RequestParam(required = false, defaultValue = "1") int amount,
            HttpServletRequest request) {

        Long userId = getCurrentUserId(request);

        switch (feature.toLowerCase()) {
            case "data_recovery":
                FeatureAccessResult dataRecoveryResult = dataRecoveryController.checkDataRecoveryAccess(userId);
                return ApiResponse.success(convertToPermissionCheckResponse(dataRecoveryResult));

            case "batch_recovery":
                QuotaAccessResult batchResult = dataRecoveryController.checkBatchRecoveryQuota(userId, amount);
                return ApiResponse.success(convertToPermissionCheckResponse(batchResult));

            case "ocr_create":
                FeatureAccessResult ocrResult = ocrController.checkOcrAccess(userId);
                return ApiResponse.success(convertToPermissionCheckResponse(ocrResult));

            case "template_count":
                QuotaAccessResult templateResult = storageController.checkTemplateCountLimit(userId);
                return ApiResponse.success(convertToPermissionCheckResponse(templateResult));

            case "storage_space":
                StorageQuotaResult storageResult = storageController.checkStorageSpaceLimit(userId, amount);
                return ApiResponse.success(convertToPermissionCheckResponse(storageResult));

            default:
                return ApiResponse.error(400, "不支持的功能类型");
        }
    }

    /**
     * 获取配额使用统计
     * GET /api/v1/vip/permissions/quotas
     */
    @GetMapping("/quotas")
    public ApiResponse<QuotaUsageResponse> getQuotaUsage(HttpServletRequest request) {
        Long userId = getCurrentUserId(request);

        QuotaUsageResponse response = QuotaUsageResponse.builder()
            .userId(userId)
            .ocr(ocrController.getOcrUsageStats(userId))
            .storage(storageController.getStorageUsageStats(userId))
            .templates(getTemplateUsageStats(userId))
            .build();

        return ApiResponse.success(response);
    }

    private DataRecoveryPermissionInfo getDataRecoveryPermission(Long userId) {
        FeatureAccessResult result = dataRecoveryController.checkDataRecoveryAccess(userId);

        return DataRecoveryPermissionInfo.builder()
            .hasPermission(result.isAllowed())
            .recoveryPeriodMonths(result.isAllowed() ? 6 : 0)
            .reason(result.isAllowed() ? null : result.getMessage())
            .upgradeInfo(result.isAllowed() ? null : result.getUpgradeInfo())
            .build();
    }

    private OcrPermissionInfo getOcrPermission(Long userId) {
        FeatureAccessResult result = ocrController.checkOcrAccess(userId);
        OcrUsageStats stats = ocrController.getOcrUsageStats(userId);

        return OcrPermissionInfo.builder()
            .hasPermission(result.isAllowed())
            .monthlyLimit(stats.getMonthlyLimit())
            .monthlyUsage(stats.getMonthlyUsage())
            .remainingCount(stats.getRemainingCount())
            .resetDate(stats.getResetDate())
            .reason(result.isAllowed() ? null : result.getMessage())
            .upgradeInfo(result.isAllowed() ? null : result.getUpgradeInfo())
            .build();
    }

    private StoragePermissionInfo getStoragePermission(Long userId) {
        StorageUsageStats stats = storageController.getStorageUsageStats(userId);

        return StoragePermissionInfo.builder()
            .totalUsage(stats.getTotalUsage())
            .storageLimit(stats.getStorageLimit())
            .remainingStorage(stats.getRemainingStorage())
            .usagePercentage(stats.getUsagePercentage())
            .templateCount(stats.getTemplateCount())
            .imageCount(stats.getImageCount())
            .documentCount(stats.getDocumentCount())
            .build();
    }
}

/**
 * 用户权限响应
 */
@Data
@Builder
public class UserPermissionsResponse {
    private Long userId;
    private DataRecoveryPermissionInfo dataRecovery;
    private OcrPermissionInfo ocrCreate;
    private StoragePermissionInfo storage;
    private TeamCollaborationPermissionInfo teamCollaboration;
    private ExperiencePermissionInfo experience;
}

/**
 * 权限检查响应
 */
@Data
@Builder
public class PermissionCheckResponse {
    private boolean allowed;
    private String feature;
    private String message;
    private String errorCode;
    private Object details;
    private UpgradeInfo upgradeInfo;
}

/**
 * 配额使用响应
 */
@Data
@Builder
public class QuotaUsageResponse {
    private Long userId;
    private OcrUsageStats ocr;
    private StorageUsageStats storage;
    private TemplateUsageStats templates;
}
```

## 4. VIP功能使用接口

### 4.1 功能使用记录接口

```java
/**
 * VIP功能使用控制器
 */
@RestController
@RequestMapping("/api/v1/vip/usage")
@Validated
public class VipUsageController {

    @Autowired
    private VipUsageService usageService;

    @Autowired
    private VipQuotaService quotaService;

    /**
     * 记录功能使用
     * POST /api/v1/vip/usage/record
     */
    @PostMapping("/record")
    public ApiResponse<UsageRecordResponse> recordUsage(
            @RequestBody @Valid RecordUsageRequest request,
            HttpServletRequest httpRequest) {

        Long userId = getCurrentUserId(httpRequest);

        try {
            UsageRecordResult result = usageService.recordFeatureUsage(
                userId, request.getFeatureType(), request.getUsageCount(), request.getMetadata());

            UsageRecordResponse response = UsageRecordResponse.builder()
                .success(result.isSuccess())
                .message(result.getMessage())
                .currentUsage(result.getCurrentUsage())
                .quotaLimit(result.getQuotaLimit())
                .remainingQuota(result.getRemainingQuota())
                .build();

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("记录功能使用失败, userId: {}, feature: {}", userId, request.getFeatureType(), e);
            return ApiResponse.error(500, "记录使用失败");
        }
    }

    /**
     * 获取功能使用统计
     * GET /api/v1/vip/usage/statistics
     */
    @GetMapping("/statistics")
    public ApiResponse<UsageStatisticsResponse> getUsageStatistics(
            @RequestParam(required = false) String feature,
            @RequestParam(required = false) String period,
            HttpServletRequest request) {

        Long userId = getCurrentUserId(request);

        UsageStatisticsResponse response = usageService.getUserUsageStatistics(
            userId, feature, period);

        return ApiResponse.success(response);
    }

    /**
     * 获取使用历史
     * GET /api/v1/vip/usage/history
     */
    @GetMapping("/history")
    public ApiResponse<PageResponse<UsageHistoryItem>> getUsageHistory(
            @RequestParam(required = false) String feature,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            HttpServletRequest request) {

        Long userId = getCurrentUserId(request);

        PageResponse<UsageHistoryItem> response = usageService.getUserUsageHistory(
            userId, feature, page, size);

        return ApiResponse.success(response);
    }
}

/**
 * 记录使用请求
 */
@Data
@Valid
public class RecordUsageRequest {
    @NotBlank
    private String featureType;

    @Min(1)
    private int usageCount = 1;

    private Map<String, Object> metadata;
}

/**
 * 使用记录响应
 */
@Data
@Builder
public class UsageRecordResponse {
    private boolean success;
    private String message;
    private int currentUsage;
    private int quotaLimit;
    private int remainingQuota;
}
```
```
